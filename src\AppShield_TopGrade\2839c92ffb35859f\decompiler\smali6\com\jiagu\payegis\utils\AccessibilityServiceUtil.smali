.class public Lcom/jiagu/payegis/utils/AccessibilityServiceUtil;
.super Ljava/lang/Object;
.source "AccessibilityServiceUtil.java"


# direct methods
.method public constructor <init>()V
    .registers 1

    .prologue
    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAccessibilityServiceList(Landroid/content/Context;)Z
    .registers 11

    .prologue
    const/4 v3, 0x1

    const/4 v2, 0x0

    .line 18
    const/4 v0, 0x7

    new-array v5, v0, [Ljava/lang/String;

    const-string v0, "com.google.android.marvin.talkback"

    aput-object v0, v5, v2

    const-string v0, "com.bjbyhd.screenreader_huawei"

    aput-object v0, v5, v3

    const/4 v0, 0x2

    const-string v1, "com.huawei.hiai"

    aput-object v1, v5, v0

    const/4 v0, 0x3

    const-string v1, "com.miui.accessibility"

    aput-object v1, v5, v0

    const/4 v0, 0x4

    const-string v1, "com.oppo.autotest.mtpservice"

    aput-object v1, v5, v0

    const/4 v0, 0x5

    const-string v1, "com.coloros.accessibilityassistant"

    aput-object v1, v5, v0

    const/4 v0, 0x6

    const-string v1, "com.samsung.android"

    aput-object v1, v5, v0

    .line 19
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 20
    invoke-virtual {p0}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "accessibility"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/accessibility/AccessibilityManager;

    .line 21
    if-nez v0, :cond_3a

    .line 40
    :cond_39
    :goto_39
    return v2

    .line 24
    :cond_3a
    const/4 v1, -0x1

    invoke-virtual {v0, v1}, Landroid/view/accessibility/AccessibilityManager;->getEnabledAccessibilityServiceList(I)Ljava/util/List;

    move-result-object v6

    .line 25
    if-eqz v6, :cond_39

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_39

    .line 29
    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v7

    move v1, v2

    :goto_4c
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_71

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/accessibilityservice/AccessibilityServiceInfo;

    .line 30
    invoke-virtual {v0}, Landroid/accessibilityservice/AccessibilityServiceInfo;->toString()Ljava/lang/String;

    move-result-object v8

    .line 31
    array-length v9, v5

    move v4, v2

    move v0, v1

    :goto_5f
    if-ge v4, v9, :cond_6f

    aget-object v1, v5, v4

    .line 32
    invoke-virtual {v8, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_6b

    .line 33
    add-int/lit8 v0, v0, 0x1

    .line 31
    :cond_6b
    add-int/lit8 v1, v4, 0x1

    move v4, v1

    goto :goto_5f

    :cond_6f
    move v1, v0

    .line 36
    goto :goto_4c

    .line 37
    :cond_71
    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v0

    if-ge v1, v0, :cond_39

    move v2, v3

    .line 38
    goto :goto_39
.end method
