.class public final Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;
.super Ljava/lang/Object;
.source "SignatureCheck.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/signaturecheck/SignatureCheck;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "DoneRunnable"
.end annotation


# instance fields
.field private final mAppPath:Ljava/lang/String;

.field private final mContext:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference",
            "<",
            "Landroid/content/Context;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method protected constructor <init>(Landroid/content/Context;Ljava/lang/String;)V
    .registers 4

    .prologue
    .line 49
    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    .line 50
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    .line 51
    iput-object p2, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mAppPath:Ljava/lang/String;

    .line 52
    return-void
.end method


# virtual methods
.method public run()V
    .registers 6

    .prologue
    .line 56
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mAppPath:Ljava/lang/String;

    # invokes: Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->getApkSignature(Ljava/lang/String;)Ljava/lang/String;
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->access$000(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 57
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    # invokes: Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->getReleaseSignature(Landroid/content/Context;)Ljava/lang/String;
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->access$100(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v2

    .line 58
    const-string v0, "Payegis"

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 59
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    # invokes: Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->checkCreator(Landroid/content/Context;)Ljava/lang/Boolean;
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->access$200(Landroid/content/Context;)Ljava/lang/Boolean;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_62

    .line 60
    sget-object v0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->ISBLOCK:Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_4f

    .line 61
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    .line 63
    :cond_4f
    invoke-static {}, Lcom/jiagu/payegis/utils/LanguageUtil;->isDomestic()Z

    move-result v0

    if-eqz v0, :cond_9a

    .line 64
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v3, "\u5b89\u5168\u63d0\u793a\uff1aAPP\u52a0\u56fa\u524d\u540e\u7b7e\u540d\u4fe1\u606f\u4e0d\u4e00\u81f4\uff0c\u8bf7\u91cd\u65b0\u786e\u8ba4\u7b7e\u540d\u4fe1\u606f\u3002"

    invoke-static {v0, v3}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    .line 69
    :cond_62
    :goto_62
    if-eqz v1, :cond_99

    if-eqz v2, :cond_99

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_99

    .line 71
    const-string v0, "Payegis"

    sget-object v1, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->ISBLOCK:Ljava/lang/Boolean;

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 72
    sget-object v0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->ISBLOCK:Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_86

    .line 73
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    .line 75
    :cond_86
    invoke-static {}, Lcom/jiagu/payegis/utils/LanguageUtil;->isDomestic()Z

    move-result v0

    if-eqz v0, :cond_a8

    .line 76
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v1, "\u5b89\u5168\u63d0\u793a\uff1aAPP\u52a0\u56fa\u524d\u540e\u7b7e\u540d\u4fe1\u606f\u4e0d\u4e00\u81f4\uff0c\u8bf7\u91cd\u65b0\u786e\u8ba4\u7b7e\u540d\u4fe1\u606f\u3002"

    invoke-static {v0, v1}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    .line 82
    :cond_99
    :goto_99
    return-void

    .line 66
    :cond_9a
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v3, "The App Sinature Is Error!"

    invoke-static {v0, v3}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    goto :goto_62

    .line 78
    :cond_a8
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v1, "The App Sinature Is Error!"

    invoke-static {v0, v1}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    goto :goto_99
.end method
