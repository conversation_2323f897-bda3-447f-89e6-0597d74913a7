.class Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;
.super Lcom/jiagu/payegis/signaturecheck/xref/WrappedX509Certificate;
.source "VerbatimX509Certificate.java"


# instance fields
.field private final mEncodedVerbatim:[B

.field private mHash:I


# direct methods
.method constructor <init>(Ljava/security/cert/X509Certificate;[B)V
    .registers 4

    .prologue
    .line 32
    invoke-direct {p0, p1}, Lcom/jiagu/payegis/signaturecheck/xref/WrappedX509Certificate;-><init>(Ljava/security/cert/X509Certificate;)V

    .line 29
    const/4 v0, -0x1

    iput v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mHash:I

    .line 33
    iput-object p2, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mEncodedVerbatim:[B

    .line 34
    return-void
.end method


# virtual methods
.method public equals(Lja<PERSON>/lang/Object;)Z
    .registers 5

    .prologue
    const/4 v0, 0x0

    .line 43
    if-ne p0, p1, :cond_5

    const/4 v0, 0x1

    .line 51
    :cond_4
    :goto_4
    return v0

    .line 44
    :cond_5
    instance-of v1, p1, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;

    if-eqz v1, :cond_4

    .line 47
    :try_start_9
    invoke-virtual {p0}, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->getEncoded()[B

    move-result-object v1

    .line 48
    check-cast p1, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;

    invoke-virtual {p1}, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->getEncoded()[B

    move-result-object v2

    .line 49
    invoke-static {v1, v2}, Ljava/util/Arrays;->equals([B[B)Z
    :try_end_16
    .catch Ljava/security/cert/CertificateEncodingException; {:try_start_9 .. :try_end_16} :catch_18

    move-result v0

    goto :goto_4

    .line 50
    :catch_18
    move-exception v1

    goto :goto_4
.end method

.method public getEncoded()[B
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateEncodingException;
        }
    .end annotation

    .prologue
    .line 38
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mEncodedVerbatim:[B

    return-object v0
.end method

.method public hashCode()I
    .registers 3

    .prologue
    .line 57
    iget v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mHash:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_f

    .line 59
    :try_start_5
    invoke-virtual {p0}, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->getEncoded()[B

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->hashCode([B)I

    move-result v0

    iput v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mHash:I
    :try_end_f
    .catch Ljava/security/cert/CertificateEncodingException; {:try_start_5 .. :try_end_f} :catch_12

    .line 64
    :cond_f
    :goto_f
    iget v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mHash:I

    return v0

    .line 60
    :catch_12
    move-exception v0

    .line 61
    const/4 v0, 0x0

    iput v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;->mHash:I

    goto :goto_f
.end method
