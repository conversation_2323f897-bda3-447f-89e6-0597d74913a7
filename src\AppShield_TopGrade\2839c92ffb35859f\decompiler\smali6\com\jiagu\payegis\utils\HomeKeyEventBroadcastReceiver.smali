.class public Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;
.super Landroid/content/BroadcastReceiver;
.source "HomeKeyEventBroadcastReceiver.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;
    }
.end annotation


# static fields
.field static final SYSTEM_HOME_KEY:Ljava/lang/String; = "homekey"

.field static final SYSTEM_REASON:Ljava/lang/String; = "reason"

.field static final SYSTEM_RECENT_APPS:Ljava/lang/String; = "recentapps"

.field static final TAG:Ljava/lang/String; = "HomeKeyEvent"

.field private static ahInfo:Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;


# instance fields
.field public isInRecentList:Z


# direct methods
.method static constructor <clinit>()V
    .registers 1

    .prologue
    .line 14
    const/4 v0, 0x0

    sput-object v0, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;->ahInfo:Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;

    return-void
.end method

.method public constructor <init>()V
    .registers 2

    .prologue
    .line 7
    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    .line 13
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;->isInRecentList:Z

    return-void
.end method

.method public static setInstance(Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;)V
    .registers 3

    .prologue
    .line 36
    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "AntiHijackActivity"

    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_e

    .line 37
    sput-object p0, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;->ahInfo:Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;

    .line 39
    :cond_e
    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .registers 5

    .prologue
    .line 18
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object v0

    .line 19
    if-eqz v0, :cond_28

    const-string v1, "android.intent.action.CLOSE_SYSTEM_DIALOGS"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_28

    .line 20
    const-string v0, "reason"

    invoke-virtual {p2, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 21
    if-eqz v0, :cond_28

    .line 22
    const-string v1, "recentapps"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_28

    .line 23
    sget-object v0, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;->ahInfo:Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;

    if-eqz v0, :cond_28

    .line 24
    sget-object v0, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver;->ahInfo:Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lcom/jiagu/payegis/utils/HomeKeyEventBroadcastReceiver$Info;->getBoolean(Z)V

    .line 29
    :cond_28
    return-void
.end method
