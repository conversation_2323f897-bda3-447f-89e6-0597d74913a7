.class Lcom/jiagu/payegis/utils/ActivityUtil$1;
.super Ljava/lang/Object;
.source "ActivityUtil.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/jiagu/payegis/utils/ActivityUtil;-><init>(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/jiagu/payegis/utils/ActivityUtil;


# direct methods
.method constructor <init>(Lcom/jiagu/payegis/utils/ActivityUtil;)V
    .registers 2

    .prologue
    .line 88
    iput-object p1, p0, Lcom/jiagu/payegis/utils/ActivityUtil$1;->this$0:Lcom/jiagu/payegis/utils/ActivityUtil;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .registers 2

    .prologue
    .line 91
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil$1;->this$0:Lcom/jiagu/payegis/utils/ActivityUtil;

    invoke-virtual {v0}, Lcom/jiagu/payegis/utils/ActivityUtil;->isQuit()Z

    move-result v0

    if-eqz v0, :cond_d

    .line 92
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil$1;->this$0:Lcom/jiagu/payegis/utils/ActivityUtil;

    invoke-virtual {v0}, Lcom/jiagu/payegis/utils/ActivityUtil;->showCoveredHint()V

    .line 94
    :cond_d
    return-void
.end method
