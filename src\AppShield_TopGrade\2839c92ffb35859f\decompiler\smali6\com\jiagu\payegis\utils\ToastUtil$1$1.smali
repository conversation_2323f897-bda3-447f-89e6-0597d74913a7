.class Lcom/jiagu/payegis/utils/ToastUtil$1$1;
.super Ljava/util/TimerTask;
.source "ToastUtil.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/jiagu/payegis/utils/ToastUtil$1;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/jiagu/payegis/utils/ToastUtil$1;


# direct methods
.method constructor <init>(Lcom/jiagu/payegis/utils/ToastUtil$1;)V
    .registers 2

    .prologue
    .line 91
    iput-object p1, p0, Lcom/jiagu/payegis/utils/ToastUtil$1$1;->this$0:Lcom/jiagu/payegis/utils/ToastUtil$1;

    invoke-direct {p0}, Ljava/util/TimerTask;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .registers 5

    .prologue
    .line 93
    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->index:I
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$100()I

    move-result v0

    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$200()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_4d

    .line 94
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "pendingMessages.get(index) : "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$200()Ljava/util/List;

    move-result-object v0

    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->index:I
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$100()I

    move-result v3

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 96
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$1$1;->this$0:Lcom/jiagu/payegis/utils/ToastUtil$1;

    iget-object v1, v0, Lcom/jiagu/payegis/utils/ToastUtil$1;->val$context:Landroid/content/Context;

    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$200()Ljava/util/List;

    move-result-object v0

    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->index:I
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$100()I

    move-result v2

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/CharSequence;

    const/4 v2, 0x0

    invoke-static {v1, v0, v2}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;I)V

    .line 97
    # operator++ for: Lcom/jiagu/payegis/utils/ToastUtil;->index:I
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$108()I

    .line 99
    :cond_4d
    return-void
.end method
