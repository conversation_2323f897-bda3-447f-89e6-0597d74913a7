.class public Lcom/jiagu/payegis/utils/AlertActivity;
.super Landroid/app/Activity;
.source "AlertActivity.java"


# static fields
.field private static MSG:Ljava/lang/String;

.field private static OKBTN:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .registers 1

    .prologue
    .line 21
    const-string v0, ""

    sput-object v0, Lcom/jiagu/payegis/utils/AlertActivity;->MSG:Ljava/lang/String;

    .line 22
    const-string v0, ""

    sput-object v0, Lcom/jiagu/payegis/utils/AlertActivity;->OKBTN:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .registers 1

    .prologue
    .line 19
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method


# virtual methods
.method protected initBaseView()Landroid/view/View;
    .registers 4

    .prologue
    const/4 v2, -0x1

    .line 49
    new-instance v0, Landroid/widget/LinearLayout;

    invoke-direct {v0, p0}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 50
    const-string v1, "#00000000"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 51
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 53
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 54
    const/16 v1, 0x31

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 56
    return-object v0
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .registers 4

    .prologue
    .line 26
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 28
    invoke-virtual {p0}, Lcom/jiagu/payegis/utils/AlertActivity;->initBaseView()Landroid/view/View;

    move-result-object v0

    .line 29
    invoke-virtual {p0, v0}, Lcom/jiagu/payegis/utils/AlertActivity;->setContentView(Landroid/view/View;)V

    .line 31
    invoke-virtual {p0}, Lcom/jiagu/payegis/utils/AlertActivity;->getIntent()Landroid/content/Intent;

    move-result-object v0

    .line 32
    const-string v1, "MSG"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/utils/AlertActivity;->MSG:Ljava/lang/String;

    .line 34
    sget-object v0, Lcom/jiagu/payegis/utils/AlertActivity;->MSG:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lcom/jiagu/payegis/utils/AlertActivity;->showDialog(Ljava/lang/String;)V

    .line 35
    return-void
.end method

.method public showDialog(Ljava/lang/String;)V
    .registers 6

    .prologue
    .line 60
    new-instance v0, Landroid/app/AlertDialog$Builder;

    invoke-direct {v0, p0}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    .line 61
    invoke-static {}, Lcom/jiagu/payegis/utils/LanguageUtil;->isDomestic()Z

    move-result v1

    if-eqz v1, :cond_33

    .line 62
    const-string v1, "\u786e\u8ba4"

    sput-object v1, Lcom/jiagu/payegis/utils/AlertActivity;->OKBTN:Ljava/lang/String;

    .line 66
    :goto_f
    invoke-virtual {v0, p1}, Landroid/app/AlertDialog$Builder;->setMessage(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v1

    sget-object v2, Lcom/jiagu/payegis/utils/AlertActivity;->OKBTN:Ljava/lang/String;

    new-instance v3, Lcom/jiagu/payegis/utils/AlertActivity$1;

    invoke-direct {v3, p0}, Lcom/jiagu/payegis/utils/AlertActivity$1;-><init>(Lcom/jiagu/payegis/utils/AlertActivity;)V

    .line 67
    invoke-virtual {v1, v2, v3}, Landroid/app/AlertDialog$Builder;->setPositiveButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    .line 89
    invoke-virtual {v0}, Landroid/app/AlertDialog$Builder;->create()Landroid/app/AlertDialog;

    move-result-object v0

    .line 90
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/app/AlertDialog;->setCancelable(Z)V

    .line 91
    invoke-virtual {v0}, Landroid/app/AlertDialog;->show()V

    .line 92
    const/4 v1, -0x1

    invoke-virtual {v0, v1}, Landroid/app/AlertDialog;->getButton(I)Landroid/widget/Button;

    move-result-object v0

    const/high16 v1, -0x1000000

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setTextColor(I)V

    .line 93
    return-void

    .line 64
    :cond_33
    const-string v1, "Confirm"

    sput-object v1, Lcom/jiagu/payegis/utils/AlertActivity;->OKBTN:Ljava/lang/String;

    goto :goto_f
.end method
