.class public Lcom/jiagu/payegis/utils/ToastUtil;
.super Ljava/lang/Object;
.source "ToastUtil.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;,
        Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;
    }
.end annotation


# static fields
.field private static final FIELD_NAME_HANDLER:Ljava/lang/String; = "mHandler"

.field private static final FIELD_NAME_TN:Ljava/lang/String; = "mTN"

.field private static final TAG:Ljava/lang/String; = "ToastUtil"

.field private static index:I

.field private static mToast:Landroid/widget/Toast;

.field private static pendingMessages:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static processName:Ljava/lang/String;

.field private static sField_TN:Ljava/lang/reflect/Field;

.field private static sField_TN_Handler:Ljava/lang/reflect/Field;

.field private static sIsHookFieldInit:Z

.field private static timer:Ljava/util/Timer;

.field private static timer2:Ljava/util/Timer;


# direct methods
.method static constructor <clinit>()V
    .registers 3

    .prologue
    const/4 v2, 0x0

    const/4 v1, 0x0

    .line 40
    sput-boolean v2, Lcom/jiagu/payegis/utils/ToastUtil;->sIsHookFieldInit:Z

    .line 43
    sput-object v1, Lcom/jiagu/payegis/utils/ToastUtil;->processName:Ljava/lang/String;

    .line 64
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;

    .line 65
    sput-object v1, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    .line 66
    sput-object v1, Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;

    .line 67
    sput v2, Lcom/jiagu/payegis/utils/ToastUtil;->index:I

    return-void
.end method

.method public constructor <init>()V
    .registers 1

    .prologue
    .line 35
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000()Ljava/util/Timer;
    .registers 1

    .prologue
    .line 35
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;

    return-object v0
.end method

.method static synthetic access$002(Ljava/util/Timer;)Ljava/util/Timer;
    .registers 1

    .prologue
    .line 35
    sput-object p0, Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;

    return-object p0
.end method

.method static synthetic access$100()I
    .registers 1

    .prologue
    .line 35
    sget v0, Lcom/jiagu/payegis/utils/ToastUtil;->index:I

    return v0
.end method

.method static synthetic access$108()I
    .registers 2

    .prologue
    .line 35
    sget v0, Lcom/jiagu/payegis/utils/ToastUtil;->index:I

    add-int/lit8 v1, v0, 0x1

    sput v1, Lcom/jiagu/payegis/utils/ToastUtil;->index:I

    return v0
.end method

.method static synthetic access$200()Ljava/util/List;
    .registers 1

    .prologue
    .line 35
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;

    return-object v0
.end method

.method static synthetic access$302(Ljava/util/Timer;)Ljava/util/Timer;
    .registers 1

    .prologue
    .line 35
    sput-object p0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    return-object p0
.end method

.method static synthetic access$400()Landroid/widget/Toast;
    .registers 1

    .prologue
    .line 35
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->mToast:Landroid/widget/Toast;

    return-object v0
.end method

.method static synthetic access$500(Landroid/widget/Toast;)V
    .registers 1

    .prologue
    .line 35
    invoke-static {p0}, Lcom/jiagu/payegis/utils/ToastUtil;->hookToast(Landroid/widget/Toast;)V

    return-void
.end method

.method public static cancelToast()V
    .registers 3

    .prologue
    .line 118
    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v0

    .line 119
    invoke-virtual {v0}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v2

    if-ne v1, v2, :cond_14

    .line 120
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->mToast:Landroid/widget/Toast;

    invoke-virtual {v0}, Landroid/widget/Toast;->cancel()V

    .line 129
    :goto_13
    return-void

    .line 122
    :cond_14
    new-instance v1, Landroid/os/Handler;

    invoke-direct {v1, v0}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v0, Lcom/jiagu/payegis/utils/ToastUtil$2;

    invoke-direct {v0}, Lcom/jiagu/payegis/utils/ToastUtil$2;-><init>()V

    invoke-virtual {v1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_13
.end method

.method public static getProcessName(Landroid/content/Context;)Ljava/lang/String;
    .registers 6

    .prologue
    .line 133
    :try_start_0
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v1

    .line 134
    const-string v0, "activity"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 135
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getRunningAppProcesses()Ljava/util/List;

    move-result-object v0

    .line 136
    if-eqz v0, :cond_69

    .line 137
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_16
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_69

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager$RunningAppProcessInfo;

    .line 138
    iget v3, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->pid:I

    if-ne v3, v1, :cond_16

    .line 139
    iget-object v0, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->processName:Ljava/lang/String;
    :try_end_28
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_28} :catch_29

    .line 163
    :goto_28
    return-object v0

    .line 143
    :catch_29
    move-exception v0

    .line 146
    :try_start_2a
    new-instance v0, Ljava/io/BufferedReader;

    new-instance v1, Ljava/io/InputStreamReader;

    new-instance v2, Ljava/io/FileInputStream;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "/proc/"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 148
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "/cmdline"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    const-string v3, "iso-8859-1"

    invoke-direct {v1, v2, v3}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;Ljava/lang/String;)V

    invoke-direct {v0, v1}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 151
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 152
    :goto_5d
    invoke-virtual {v0}, Ljava/io/BufferedReader;->read()I

    move-result v2

    if-lez v2, :cond_6b

    .line 153
    int-to-char v2, v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_5d

    .line 159
    :catch_68
    move-exception v0

    .line 163
    :cond_69
    const/4 v0, 0x0

    goto :goto_28

    .line 155
    :cond_6b
    if-eqz v0, :cond_70

    .line 156
    invoke-virtual {v0}, Ljava/io/BufferedReader;->close()V

    .line 158
    :cond_70
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;
    :try_end_73
    .catch Ljava/io/IOException; {:try_start_2a .. :try_end_73} :catch_68

    move-result-object v0

    goto :goto_28
.end method

.method private static hookToast(Landroid/widget/Toast;)V
    .registers 5

    .prologue
    .line 172
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->isNeedHook()Z

    move-result v0

    if-nez v0, :cond_7

    .line 189
    :goto_6
    return-void

    .line 176
    :cond_7
    :try_start_7
    sget-boolean v0, Lcom/jiagu/payegis/utils/ToastUtil;->sIsHookFieldInit:Z

    if-nez v0, :cond_32

    .line 177
    const-class v0, Landroid/widget/Toast;

    const-string v1, "mTN"

    invoke-virtual {v0, v1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN:Ljava/lang/reflect/Field;

    .line 178
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN:Ljava/lang/reflect/Field;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    .line 179
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN:Ljava/lang/reflect/Field;

    invoke-virtual {v0}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    move-result-object v0

    const-string v1, "mHandler"

    invoke-virtual {v0, v1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN_Handler:Ljava/lang/reflect/Field;

    .line 180
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN_Handler:Ljava/lang/reflect/Field;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    .line 181
    const/4 v0, 0x1

    sput-boolean v0, Lcom/jiagu/payegis/utils/ToastUtil;->sIsHookFieldInit:Z

    .line 183
    :cond_32
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN:Ljava/lang/reflect/Field;

    invoke-virtual {v0, p0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    .line 184
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN_Handler:Ljava/lang/reflect/Field;

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/os/Handler;

    .line 185
    sget-object v2, Lcom/jiagu/payegis/utils/ToastUtil;->sField_TN_Handler:Ljava/lang/reflect/Field;

    new-instance v3, Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;

    invoke-direct {v3, v0}, Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;-><init>(Landroid/os/Handler;)V

    invoke-virtual {v2, v1, v3}, Ljava/lang/reflect/Field;->set(Ljava/lang/Object;Ljava/lang/Object;)V
    :try_end_4a
    .catch Ljava/lang/Exception; {:try_start_7 .. :try_end_4a} :catch_4b

    goto :goto_6

    .line 186
    :catch_4b
    move-exception v0

    .line 187
    const-string v1, "ToastUtil"

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Hook toast exception="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_6
.end method

.method private static isNeedHook()Z
    .registers 2

    .prologue
    .line 197
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x19

    if-eq v0, v1, :cond_c

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-ne v0, v1, :cond_e

    :cond_c
    const/4 v0, 0x1

    :goto_d
    return v0

    :cond_e
    const/4 v0, 0x0

    goto :goto_d
.end method

.method public static showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V
    .registers 6

    .prologue
    .line 69
    invoke-static {p0}, Lcom/jiagu/payegis/utils/ToastUtil;->getProcessName(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->processName:Ljava/lang/String;

    .line 70
    invoke-virtual {p0}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v0

    .line 71
    sget-object v1, Lcom/jiagu/payegis/utils/ToastUtil;->processName:Ljava/lang/String;

    const-string v2, ":"

    invoke-virtual {v1, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_15

    .line 112
    :cond_14
    :goto_14
    return-void

    .line 73
    :cond_15
    sget-object v1, Lcom/jiagu/payegis/utils/ToastUtil;->processName:Ljava/lang/String;

    invoke-static {v1, v0}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_14

    .line 76
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    if-eqz v0, :cond_29

    .line 78
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    .line 79
    const/4 v0, 0x0

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    .line 81
    :cond_29
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->pendingMessages:Ljava/util/List;

    invoke-interface {p1}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 85
    new-instance v0, Ljava/util/Timer;

    invoke-direct {v0}, Ljava/util/Timer;-><init>()V

    sput-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    .line 86
    sget-object v0, Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;

    new-instance v1, Lcom/jiagu/payegis/utils/ToastUtil$1;

    invoke-direct {v1, p0}, Lcom/jiagu/payegis/utils/ToastUtil$1;-><init>(Landroid/content/Context;)V

    const-wide/16 v2, 0x64

    invoke-virtual {v0, v1, v2, v3}, Ljava/util/Timer;->schedule(Ljava/util/TimerTask;J)V

    goto :goto_14
.end method

.method public static showToast(Landroid/content/Context;Ljava/lang/CharSequence;I)V
    .registers 6

    .prologue
    .line 52
    new-instance v0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;

    invoke-direct {v0, p0, p1, p2}, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;-><init>(Landroid/content/Context;Ljava/lang/CharSequence;I)V

    .line 53
    instance-of v1, p0, Landroid/app/Activity;

    if-eqz v1, :cond_17

    .line 54
    check-cast p0, Landroid/app/Activity;

    .line 55
    if-eqz p0, :cond_16

    invoke-virtual {p0}, Landroid/app/Activity;->isFinishing()Z

    move-result v1

    if-nez v1, :cond_16

    .line 56
    invoke-virtual {p0, v0}, Landroid/app/Activity;->runOnUiThread(Ljava/lang/Runnable;)V

    .line 62
    :cond_16
    :goto_16
    return-void

    .line 59
    :cond_17
    new-instance v1, Landroid/os/Handler;

    invoke-virtual {p0}, Landroid/content/Context;->getMainLooper()Landroid/os/Looper;

    move-result-object v2

    invoke-direct {v1, v2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    .line 60
    invoke-virtual {v1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_16
.end method
