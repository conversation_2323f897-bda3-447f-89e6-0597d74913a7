.class public Lcom/jiagu/payegis/rootcheck/RootCheck;
.super Ljava/lang/Object;
.source "RootCheck.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;
    }
.end annotation


# static fields
.field private static final MSG:Ljava/lang/String; = "This Phone Is Rooted!"

.field private static final MSG_CN:Ljava/lang/String; = "\u624b\u673a\u5df2\u88abroot\uff0c\u5b58\u5728\u4fe1\u606f\u6cc4\u6f0f\u548c\u5b89\u5168\u98ce\u9669\uff0c\u8bf7\u91cd\u65b0\u914d\u7f6e\u64cd\u4f5c\u7cfb\u7edf\u3002"

.field private static blackListedMountPaths:[Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .registers 3

    .prologue
    .line 25
    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "/sbin/.magisk/"

    aput-object v2, v0, v1

    const/4 v1, 0x1

    const-string v2, "/sbin/.core/mirror"

    aput-object v2, v0, v1

    const/4 v1, 0x2

    const-string v2, "/sbin/.core/img"

    aput-object v2, v0, v1

    const/4 v1, 0x3

    const-string v2, "/sbin/.core/db-0/magisk.db"

    aput-object v2, v0, v1

    const/4 v1, 0x4

    const-string v2, "magisk"

    aput-object v2, v0, v1

    sput-object v0, Lcom/jiagu/payegis/rootcheck/RootCheck;->blackListedMountPaths:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .registers 1

    .prologue
    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static blockRoot(Landroid/content/Context;)V
    .registers 2

    .prologue
    .line 92
    invoke-static {}, Lcom/jiagu/payegis/rootcheck/RootCheck;->checkRootFile()Z

    move-result v0

    if-nez v0, :cond_d

    const/4 v0, 0x1

    invoke-static {v0}, Lcom/jiagu/payegis/rootcheck/RootCheck;->isMagiskPresent(I)Z

    move-result v0

    if-eqz v0, :cond_14

    .line 93
    :cond_d
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    .line 95
    :cond_14
    return-void
.end method

.method public static checkRootFile()Z
    .registers 7

    .prologue
    const/4 v0, 0x1

    const/4 v1, 0x0

    .line 52
    const/16 v2, 0xe

    new-array v3, v2, [Ljava/lang/String;

    const-string v2, "/sbin/su"

    aput-object v2, v3, v1

    const-string v2, "/system/bin/su"

    aput-object v2, v3, v0

    const/4 v2, 0x2

    const-string v4, "/system/xbin/su"

    aput-object v4, v3, v2

    const/4 v2, 0x3

    const-string v4, "/data/local/xbin/su"

    aput-object v4, v3, v2

    const/4 v2, 0x4

    const-string v4, "/data/local/bin/su"

    aput-object v4, v3, v2

    const/4 v2, 0x5

    const-string v4, "/system/sd/xbin/su"

    aput-object v4, v3, v2

    const/4 v2, 0x6

    const-string v4, "/system/bin/failsafe/su"

    aput-object v4, v3, v2

    const/4 v2, 0x7

    const-string v4, "/data/local/su"

    aput-object v4, v3, v2

    const/16 v2, 0x8

    const-string v4, "/su/bin/su"

    aput-object v4, v3, v2

    const/16 v2, 0x9

    const-string v4, "/system/bin/.ext/su"

    aput-object v4, v3, v2

    const/16 v2, 0xa

    const-string v4, "/system/usr/we-need-root/su"

    aput-object v4, v3, v2

    const/16 v2, 0xb

    const-string v4, "/cache/su"

    aput-object v4, v3, v2

    const/16 v2, 0xc

    const-string v4, "/data/su"

    aput-object v4, v3, v2

    const/16 v2, 0xd

    const-string v4, "/dev/su"

    aput-object v4, v3, v2

    .line 58
    array-length v4, v3

    move v2, v1

    :goto_52
    if-ge v2, v4, :cond_65

    aget-object v5, v3, v2

    .line 59
    new-instance v6, Ljava/io/File;

    invoke-direct {v6, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 60
    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v5

    if-eqz v5, :cond_62

    .line 62
    :goto_61
    return v0

    .line 58
    :cond_62
    add-int/lit8 v2, v2, 0x1

    goto :goto_52

    :cond_65
    move v0, v1

    .line 62
    goto :goto_61
.end method

.method public static checkSuFile()Z
    .registers 7

    .prologue
    const/4 v0, 0x1

    const/4 v1, 0x0

    .line 36
    const/4 v2, 0x0

    .line 39
    :try_start_3
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v3

    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/String;

    const/4 v5, 0x0

    const-string v6, "which"

    aput-object v6, v4, v5

    const/4 v5, 0x1

    const-string v6, "su"

    aput-object v6, v4, v5

    invoke-virtual {v3, v4}, Ljava/lang/Runtime;->exec([Ljava/lang/String;)Ljava/lang/Process;
    :try_end_17
    .catch Ljava/lang/Throwable; {:try_start_3 .. :try_end_17} :catch_39
    .catchall {:try_start_3 .. :try_end_17} :catchall_42

    move-result-object v2

    .line 40
    :try_start_18
    new-instance v3, Ljava/io/BufferedReader;

    new-instance v4, Ljava/io/InputStreamReader;

    invoke-virtual {v2}, Ljava/lang/Process;->getInputStream()Ljava/io/InputStream;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v3, v4}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 41
    invoke-virtual {v3}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;
    :try_end_29
    .catch Ljava/lang/Throwable; {:try_start_18 .. :try_end_29} :catch_49
    .catchall {:try_start_18 .. :try_end_29} :catchall_42

    move-result-object v3

    if-eqz v3, :cond_32

    .line 46
    if-eqz v2, :cond_31

    invoke-virtual {v2}, Ljava/lang/Process;->destroy()V

    .line 44
    :cond_31
    :goto_31
    return v0

    .line 46
    :cond_32
    if-eqz v2, :cond_37

    invoke-virtual {v2}, Ljava/lang/Process;->destroy()V

    :cond_37
    move v0, v1

    .line 42
    goto :goto_31

    .line 43
    :catch_39
    move-exception v0

    move-object v0, v2

    .line 46
    :goto_3b
    if-eqz v0, :cond_40

    invoke-virtual {v0}, Ljava/lang/Process;->destroy()V

    :cond_40
    move v0, v1

    .line 44
    goto :goto_31

    .line 46
    :catchall_42
    move-exception v0

    if-eqz v2, :cond_48

    invoke-virtual {v2}, Ljava/lang/Process;->destroy()V

    .line 47
    :cond_48
    throw v0

    .line 43
    :catch_49
    move-exception v0

    move-object v0, v2

    goto :goto_3b
.end method

.method public static isMagiskPresent(I)Z
    .registers 11

    .prologue
    const/4 v1, 0x1

    const/4 v2, 0x0

    .line 99
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    .line 101
    const-string v3, "/proc/%d/mounts"

    new-array v4, v1, [Ljava/lang/Object;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v4, v2

    invoke-static {v3, v4}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 102
    new-instance v3, Ljava/io/File;

    invoke-direct {v3, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 104
    :try_start_19
    new-instance v4, Ljava/io/FileInputStream;

    invoke-direct {v4, v3}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V

    .line 105
    new-instance v5, Ljava/io/BufferedReader;

    new-instance v0, Ljava/io/InputStreamReader;

    invoke-direct {v0, v4}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v5, v0}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    move v0, v2

    .line 108
    :cond_29
    invoke-virtual {v5}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_42

    .line 110
    sget-object v7, Lcom/jiagu/payegis/rootcheck/RootCheck;->blackListedMountPaths:[Ljava/lang/String;

    array-length v8, v7

    move v3, v2

    :goto_33
    if-ge v3, v8, :cond_29

    aget-object v9, v7, v3

    .line 111
    invoke-virtual {v6, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v9

    if-eqz v9, :cond_3f

    .line 113
    add-int/lit8 v0, v0, 0x1

    .line 110
    :cond_3f
    add-int/lit8 v3, v3, 0x1

    goto :goto_33

    .line 117
    :cond_42
    invoke-virtual {v5}, Ljava/io/BufferedReader;->close()V

    .line 118
    invoke-virtual {v4}, Ljava/io/FileInputStream;->close()V
    :try_end_48
    .catch Ljava/io/IOException; {:try_start_19 .. :try_end_48} :catch_6b

    .line 120
    if-lez p0, :cond_64

    .line 121
    if-le v0, v1, :cond_4e

    move v0, v1

    .line 139
    :goto_4d
    return v0

    .line 127
    :cond_4e
    :try_start_4e
    const-string v0, "com.payegis.FirstApplication"

    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    .line 128
    const-string v1, "interfaceMC"

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Class;

    invoke-virtual {v0, v1, v3}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0

    .line 129
    const/4 v1, 0x0

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v0, v1, v3}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_64
    .catch Ljava/lang/Exception; {:try_start_4e .. :try_end_64} :catch_66
    .catch Ljava/io/IOException; {:try_start_4e .. :try_end_64} :catch_6b

    :cond_64
    :goto_64
    move v0, v2

    .line 139
    goto :goto_4d

    .line 130
    :catch_66
    move-exception v0

    .line 131
    :try_start_67
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_6a
    .catch Ljava/io/IOException; {:try_start_67 .. :try_end_6a} :catch_6b

    goto :goto_64

    .line 136
    :catch_6b
    move-exception v0

    .line 137
    invoke-virtual {v0}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_64
.end method

.method public static toastRoot(Landroid/content/Context;)V
    .registers 5

    .prologue
    .line 83
    invoke-static {}, Lcom/jiagu/payegis/rootcheck/RootCheck;->checkRootFile()Z

    move-result v0

    if-nez v0, :cond_d

    const/4 v0, -0x1

    invoke-static {v0}, Lcom/jiagu/payegis/rootcheck/RootCheck;->isMagiskPresent(I)Z

    move-result v0

    if-eqz v0, :cond_1c

    .line 84
    :cond_d
    new-instance v0, Landroid/os/Handler;

    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    .line 85
    new-instance v1, Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;

    invoke-direct {v1, p0}, Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;-><init>(Landroid/content/Context;)V

    const-wide/16 v2, 0x46

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 87
    :cond_1c
    return-void
.end method
