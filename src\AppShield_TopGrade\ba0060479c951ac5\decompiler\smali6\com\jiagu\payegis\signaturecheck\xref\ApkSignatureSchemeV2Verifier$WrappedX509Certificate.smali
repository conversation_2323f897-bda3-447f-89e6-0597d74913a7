.class Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;
.super Ljava/security/cert/X509Certificate;
.source "ApkSignatureSchemeV2Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "WrappedX509Certificate"
.end annotation


# instance fields
.field private final wrapped:Ljava/security/cert/X509Certificate;


# direct methods
.method public constructor <init>(Ljava/security/cert/X509Certificate;)V
    .registers 2

    .prologue
    .line 770
    invoke-direct {p0}, Ljava/security/cert/X509Certificate;-><init>()V

    .line 771
    iput-object p1, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    .line 772
    return-void
.end method


# virtual methods
.method public checkValidity()V
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateExpiredException;,
            Ljava/security/cert/CertificateNotYetValidException;
        }
    .end annotation

    .prologue
    .line 797
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->checkValidity()V

    .line 798
    return-void
.end method

.method public checkValidity(Ljava/util/Date;)V
    .registers 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateExpiredException;,
            Ljava/security/cert/CertificateNotYetValidException;
        }
    .end annotation

    .prologue
    .line 803
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0, p1}, Ljava/security/cert/X509Certificate;->checkValidity(Ljava/util/Date;)V

    .line 804
    return-void
.end method

.method public getBasicConstraints()I
    .registers 2

    .prologue
    .line 878
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getBasicConstraints()I

    move-result v0

    return v0
.end method

.method public getCriticalExtensionOIDs()Ljava/util/Set;
    .registers 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .prologue
    .line 776
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getCriticalExtensionOIDs()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public getEncoded()[B
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateEncodingException;
        }
    .end annotation

    .prologue
    .line 883
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getEncoded()[B

    move-result-object v0

    return-object v0
.end method

.method public getExtensionValue(Ljava/lang/String;)[B
    .registers 3

    .prologue
    .line 781
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0, p1}, Ljava/security/cert/X509Certificate;->getExtensionValue(Ljava/lang/String;)[B

    move-result-object v0

    return-object v0
.end method

.method public getIssuerDN()Ljava/security/Principal;
    .registers 2

    .prologue
    .line 818
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getIssuerDN()Ljava/security/Principal;

    move-result-object v0

    return-object v0
.end method

.method public getIssuerUniqueID()[Z
    .registers 2

    .prologue
    .line 863
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getIssuerUniqueID()[Z

    move-result-object v0

    return-object v0
.end method

.method public getKeyUsage()[Z
    .registers 2

    .prologue
    .line 873
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getKeyUsage()[Z

    move-result-object v0

    return-object v0
.end method

.method public getNonCriticalExtensionOIDs()Ljava/util/Set;
    .registers 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .prologue
    .line 786
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getNonCriticalExtensionOIDs()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public getNotAfter()Ljava/util/Date;
    .registers 2

    .prologue
    .line 833
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getNotAfter()Ljava/util/Date;

    move-result-object v0

    return-object v0
.end method

.method public getNotBefore()Ljava/util/Date;
    .registers 2

    .prologue
    .line 828
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getNotBefore()Ljava/util/Date;

    move-result-object v0

    return-object v0
.end method

.method public getPublicKey()Ljava/security/PublicKey;
    .registers 2

    .prologue
    .line 906
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getPublicKey()Ljava/security/PublicKey;

    move-result-object v0

    return-object v0
.end method

.method public getSerialNumber()Ljava/math/BigInteger;
    .registers 2

    .prologue
    .line 813
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSerialNumber()Ljava/math/BigInteger;

    move-result-object v0

    return-object v0
.end method

.method public getSigAlgName()Ljava/lang/String;
    .registers 2

    .prologue
    .line 848
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSigAlgName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getSigAlgOID()Ljava/lang/String;
    .registers 2

    .prologue
    .line 853
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSigAlgOID()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getSigAlgParams()[B
    .registers 2

    .prologue
    .line 858
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSigAlgParams()[B

    move-result-object v0

    return-object v0
.end method

.method public getSignature()[B
    .registers 2

    .prologue
    .line 843
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSignature()[B

    move-result-object v0

    return-object v0
.end method

.method public getSubjectDN()Ljava/security/Principal;
    .registers 2

    .prologue
    .line 823
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSubjectDN()Ljava/security/Principal;

    move-result-object v0

    return-object v0
.end method

.method public getSubjectUniqueID()[Z
    .registers 2

    .prologue
    .line 868
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getSubjectUniqueID()[Z

    move-result-object v0

    return-object v0
.end method

.method public getTBSCertificate()[B
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateEncodingException;
        }
    .end annotation

    .prologue
    .line 838
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getTBSCertificate()[B

    move-result-object v0

    return-object v0
.end method

.method public getVersion()I
    .registers 2

    .prologue
    .line 808
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getVersion()I

    move-result v0

    return v0
.end method

.method public hasUnsupportedCriticalExtension()Z
    .registers 2

    .prologue
    .line 791
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->hasUnsupportedCriticalExtension()Z

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .registers 2

    .prologue
    .line 901
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public verify(Ljava/security/PublicKey;)V
    .registers 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateException;,
            Ljava/security/NoSuchAlgorithmException;,
            Ljava/security/InvalidKeyException;,
            Ljava/security/NoSuchProviderException;,
            Ljava/security/SignatureException;
        }
    .end annotation

    .prologue
    .line 889
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0, p1}, Ljava/security/cert/X509Certificate;->verify(Ljava/security/PublicKey;)V

    .line 890
    return-void
.end method

.method public verify(Ljava/security/PublicKey;Ljava/lang/String;)V
    .registers 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateException;,
            Ljava/security/NoSuchAlgorithmException;,
            Ljava/security/InvalidKeyException;,
            Ljava/security/NoSuchProviderException;,
            Ljava/security/SignatureException;
        }
    .end annotation

    .prologue
    .line 896
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;->wrapped:Ljava/security/cert/X509Certificate;

    invoke-virtual {v0, p1, p2}, Ljava/security/cert/X509Certificate;->verify(Ljava/security/PublicKey;Ljava/lang/String;)V

    .line 897
    return-void
.end method
