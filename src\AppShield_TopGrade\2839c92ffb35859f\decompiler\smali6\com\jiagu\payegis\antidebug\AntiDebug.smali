.class public Lcom/jiagu/payegis/antidebug/AntiDebug;
.super Ljava/lang/Object;
.source "AntiDebug.java"


# direct methods
.method public constructor <init>()V
    .registers 1

    .prologue
    .line 6
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static CheckDebug(Landroid/content/Context;)V
    .registers 2

    .prologue
    .line 9
    invoke-static {p0}, Lcom/jiagu/payegis/antidebug/AntiDebug;->isDebuggable(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_a

    .line 10
    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/System;->exit(I)V

    .line 12
    :cond_a
    return-void
.end method

.method private static isDebuggable(Landroid/content/Context;)Z
    .registers 3

    .prologue
    const/4 v0, 0x0

    .line 16
    :try_start_1
    invoke-virtual {p0}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v1

    .line 17
    iget v1, v1, Landroid/content/pm/ApplicationInfo;->flags:I
    :try_end_7
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_7} :catch_d

    and-int/lit8 v1, v1, 0x2

    if-eqz v1, :cond_c

    const/4 v0, 0x1

    .line 19
    :cond_c
    :goto_c
    return v0

    .line 18
    :catch_d
    move-exception v1

    goto :goto_c
.end method
