2025-08-27 15:13:03.226   576-655   DisplayModeController   surfaceflinger                       D  setDesiredMode 4619827677550801152 {mode={fps=60.00 Hz, modePtr={id=0, vsyncRate=60.00 Hz, peakRefreshRate=60.00 Hz}}, emitEvent=false, force=false}
2025-08-27 15:13:03.241   579-625   hwc-display             and...graphics.composer@2.4-service  D  setActiveConfigWithConstraints:: PrimaryDisplay config(36) test(0)
2025-08-27 15:13:03.241   579-625   hwc-display             and...graphics.composer@2.4-service  I  [PrimaryDisplay] setActiveConfigWithConstraints: config(36)
2025-08-27 15:13:03.513   576-653   DisplayModeController   surfaceflinger                       D  setDesiredMode 4619827677550801152 {mode={fps=90.00 Hz, modePtr={id=1, vsyncRate=90.00 Hz, peakRefreshRate=90.00 Hz}}, emitEvent=true, force=false}
2025-08-27 15:13:03.518   579-625   hwc-display             and...graphics.composer@2.4-service  D  setActiveConfigWithConstraints:: PrimaryDisplay config(37) test(0)
2025-08-27 15:13:03.519   579-625   hwc-display             and...graphics.composer@2.4-service  I  [PrimaryDisplay] setActiveConfigWithConstraints: config(37)
2025-08-27 15:13:03.557  2088-2115  deeptouch               and...dware.input.processor-service  I  I0000 00:00:1756278783.557740    2115 tf_lite_classifier.cc:413] Event stream classified as kNone
2025-08-27 15:13:03.562  1362-11755 Grammatica...ctionUtils system_server                        V  AttributionSource: android.content.AttributionSource@4fd544ad does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
2025-08-27 15:13:03.563  1362-11755 OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_RESIZEABLE_ACTIVITY_OVERRIDES
2025-08-27 15:13:03.563  1362-11755 OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_MIN_ASPECT_RATIO_OVERRIDE
2025-08-27 15:13:03.563   976-9413  audioserver             audioserver                          D  logFgsApiBegin: FGS Logger Transaction failed, -129
2025-08-27 15:13:03.563  1362-2968  AS.PlaybackActivityMon  system_server                        W  No piid assigned for invalid/internal port id 34
2025-08-27 15:13:03.565   882-906   audio_hw_35l41          android.hardware.audio.service       W  cs35l41_amp_common_event: ret: 0, event: 15, state: 2, device: 0
2025-08-27 15:13:03.565   882-906   audio_hw_output_stream  android.hardware.audio.service       D  update stream 1 active 1 gain 0.061406
2025-08-27 15:13:03.565   882-3625  audio_hw                android.hardware.audio.service       D  prepare low-latency-playback
2025-08-27 15:13:03.565   882-3625  audio_hw_soundtrigger   android.hardware.audio.service       D  st_uc_monitor:uc:low-latency-playback active
2025-08-27 15:13:03.565   882-3625  audio_hw                android.hardware.audio.service       D  low-latency-playback:
2025-08-27 15:13:03.565   882-3625  audio_hw                android.hardware.audio.service       D  rx:
2025-08-27 15:13:03.565   882-3625  audio_hw                android.hardware.audio.service       D    #0: OUT_SPEAKER_BE_CFG 0
2025-08-27 15:13:03.565   882-3625  audio_hw_35l41          android.hardware.audio.service       W  cs35l41_amp_common_event: ret: 0, event: 1, state: 2, device: 4
2025-08-27 15:13:03.565   882-3625  audio_route             android.hardware.audio.service       D  Apply path: speaker
2025-08-27 15:13:03.566   882-3625  audio_hw_aoc_route      android.hardware.audio.service       D  speaker 1
2025-08-27 15:13:03.566   882-3625  audio_route             android.hardware.audio.service       D  Apply path: hostless-ulC spk-vi
2025-08-27 15:13:03.568  1362-11755 OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_ORIENTATION_OVERRIDE
2025-08-27 15:13:03.569   882-3625  audio_hw_35l41          android.hardware.audio.service       W  cs35l41_amp_common_event: ret: 0, event: 2, state: 3, device: 4
2025-08-27 15:13:03.569   882-3625  audio_hw_soundtrigger   android.hardware.audio.service       D  st_comm_aud_event_monitor:codec dev:4 active
2025-08-27 15:13:03.569   882-3625  audio_hw_aoc            android.hardware.audio.service       I  Mode Ambient is already selected
2025-08-27 15:13:03.569   882-3625  audio_route             android.hardware.audio.service       D  Apply path: low-latency-playbackP
2025-08-27 15:13:03.569   882-3625  audio_hw_aoc_route      android.hardware.audio.service       D  low-latency-playbackP 1
2025-08-27 15:13:03.570   882-3625  audio_route             android.hardware.audio.service       E  unable to find path 'speaker-post'
2025-08-27 15:13:03.570  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 273: AudioOutCtrl: cmd ID: 0x010f, tag: 0xa1 [cntr = 781]
2025-08-27 15:13:03.570   882-3625  audio_hw_aoc_route      android.hardware.audio.service       D  speaker-post 1
2025-08-27 15:13:03.570  1177-1177  AOC                     aocd                                 D  F1:Source 1 mastered by sink 0
2025-08-27 15:13:03.570  1177-1177  AOC                     aocd                                 D  F1:AT Map:2 (LL:0), 1 EPs active, Power: Yes, Config: No
2025-08-27 15:13:03.570  1177-1177  AOC                     aocd                                 D  F1:Mixer 0 configuration changed: (2)
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:Sink 0 Configuration changed: ULL (enabled)
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:AHWSinkSPKR started: 48 samples (32-bit,2 ch,48 kHz) block 384
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:[AHWSinkSPKR] DL resync B: 0, A:0. wo: 0, target offset: 1632 (align 0) -> ro:0
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 811: AudioOutCtrl: ipc: audio_output_co, cmd ID: 0x010f, tag: 0xa1, rc: 0
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:Speaker Started (DualDMA on 1 and 2)
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  H0:Mixer AMixSPKR: 480 samples (0002/0002) (ON SC)
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  H0:	 1: 0x403e1ac0
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  H0:AMixSPKR PostProcessing Config: WAVES
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  H0:Mixer configuration updated
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  H0:Mixer state changed
2025-08-27 15:13:03.571  1177-1177  AOC                     aocd                                 D  F1:[AHWSinkSPKR] mix overrun by 3840
2025-08-27 15:13:03.574  1362-11755 ActivityTaskManager     system_server                        I  START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity bnds=[540,328][787,618]} with LAUNCH_SINGLE_TASK from uid 10214 (sr=191114288) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
2025-08-27 15:13:03.574  2367-2367  StatsLog                com...le.android.apps.nexuslauncher  D  LAUNCHER_APP_LAUNCH_TAP
2025-08-27 15:13:03.574  2367-2367  StatsLog                com...le.android.apps.nexuslauncher  D  LAUNCHER_HOTSEAT_RANKED
2025-08-27 15:13:03.574  1997-2022  WindowManagerShell      com.android.systemui                 V  Transition requested (#344): android.os.BinderProxy@dcd1c5e TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=199 effectiveUid=10292 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity } baseActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} topActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} origActivity=null realActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} numActivities=1 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@dade83f} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 128 - 0, 0) topActivityInfo=ActivityInfo{f89450c com.sk.oa.ui.activity.SplashActivity} launchCookies=[android.os.BinderProxy@3c43e55] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(276, 692 - 804, 1772) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@4aa06a, appThread = android.app.IApplicationThread$Stub$Proxy@8a2c95b, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 344 }
2025-08-27 15:13:03.574  1997-2022  ShellSplitScreen        com.android.systemui                 D  logExit: no-op, mLoggerSessionId is null
2025-08-27 15:13:03.574  1997-2022  WindowManagerShell      com.android.systemui                 V  RemoteTransition directly requested for (#344) android.os.BinderProxy@dcd1c5e: RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@4aa06a, appThread = android.app.IApplicationThread$Stub$Proxy@8a2c95b, debugName = QuickstepLaunch }
2025-08-27 15:13:03.575   576-622   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
2025-08-27 15:13:03.575  2367-2367  SplitSelectStateCtor    com...le.android.apps.nexuslauncher  W  Missing session instanceIds
2025-08-27 15:13:03.575  2367-2367  StatsLog                com...le.android.apps.nexuslauncher  D  LAUNCHER_SPLIT_SELECTION_EXIT_INTERRUPTED
2025-08-27 15:13:03.576   882-3625  audio_hw_waves          android.hardware.audio.service       D  waves_usecase_event: low-latency-playback start
2025-08-27 15:13:03.576   882-3625  audio_hw_waves          android.hardware.audio.service       D  waves_usecase_event: trigger to update tuning when first track is running.
2025-08-27 15:13:03.576   882-3625  audio_hw_waves          android.hardware.audio.service       D  update_sink_info_from_usecases: sink 1 VOL/Stream update 0.000000(4294967295) -> 0.061406(1)
2025-08-27 15:13:03.576  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 273: AudioOutCtrl: cmd ID: 0x00ce, tag: 0xa2 [cntr = 782]
2025-08-27 15:13:03.576  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 811: AudioOutCtrl: ipc: audio_output_co, cmd ID: 0x0120, tag: 0xd8, rc: 0
2025-08-27 15:13:03.577  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.577   882-3625  audio_hw_waves          android.hardware.audio.service       D  send_waves_tuning: instance_id = 1, tuning_id = 2
2025-08-27 15:13:03.577   882-3625  audio_hw_aoc            android.hardware.audio.service       D  aoc_send_rtc_mixer_tuning: block_id = 16, comp_id = 2
2025-08-27 15:13:03.577  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 273: AudioOutCtrl: cmd ID: 0x00d5, tag: 0x7e [cntr = 783]
2025-08-27 15:13:03.579  1177-1177  AOC                     aocd                                 D  H0:MSG: controller.cc, 811: AudOutCtrlH0: ipc: C-AO-H0, cmd ID: 0x00d5, tag: 0x8d, rc: 0
2025-08-27 15:13:03.579  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 811: AudioOutCtrl: ipc: audio_output_tu, cmd ID: 0x00d5, tag: 0x8d, rc: 0
2025-08-27 15:13:03.581  2367-2432  HWUI                    com...le.android.apps.nexuslauncher  W  Image decoding logging dropped!
2025-08-27 15:13:03.581  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 273: AudioOutCtrl: cmd ID: 0x00c9, tag: 0xa3 [cntr = 784]
2025-08-27 15:13:03.581  1177-1177  AOC                     aocd                                 D  F1:MSG: controller.cc, 811: AudioOutCtrl: ipc: audio_output_co, cmd ID: 0x00c9, tag: 0xa3, rc: 0
2025-08-27 15:13:03.581  1362-13116 ActivityManager         system_server                        D  quick sync unfreeze 6094 for 1
2025-08-27 15:13:03.585  2367-2432  QuickstepModelDelegate  com...le.android.apps.nexuslauncher  D  notifyAppTargetEvent action=1 launchLocation=predictions
2025-08-27 15:13:03.585  1362-1696  ActivityManager         system_server                        D  sync unfroze 6094 com.ffcs.henan.app for 1
2025-08-27 15:13:03.586   576-576   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
2025-08-27 15:13:03.586  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.587  1997-2034  HWUI                    com.android.systemui                 W  Image decoding logging dropped!
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  Foreground UID status:
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10079 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10091 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10122 is in foreground: true
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10169 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10203 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10214 is in foreground: true
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10238 is in foreground: true
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10260 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10264 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10268 is in foreground: true
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10273 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10280 is in foreground: false
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10292 is in foreground: true
2025-08-27 15:13:03.587  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10293 is in foreground: false
2025-08-27 15:13:03.589  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.589  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.589  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.595  6094-6094  .ffcs.henan.app         com.ffcs.henan.app                   I  AssetManager2(0xb4000076d3d55658) locale list changing from [] to [zh-Hans-CN]
2025-08-27 15:13:03.596  1362-1817  ActivityManager         system_server                        D  sync unfroze 32439 com.google.android.apps.turbo for 6
2025-08-27 15:13:03.597  1362-1817  ProcessStats            system_server                        W  Tracking association SourceState{103c808 system/1000 ImpBg #199301} whose proc state 6 is better than process ProcessState{688bf6e com.google.android.apps.turbo/10130 pkg=com.google.android.apps.turbo} proc state 14 (7 skipped)
2025-08-27 15:13:03.600 32439-32457 Turbo.Powe...WindowImpl com.google.android.apps.turbo        W  PowerBrain is not active
2025-08-27 15:13:03.601  1362-13116 CoreBackPreview         system_server                        D  Window{f4cd143 u0 Splash Screen com.ffcs.henan.app}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@d886bb5, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
2025-08-27 15:13:03.607  1362-1693  WindowManager           system_server                        V  Sent Transition (#344) createdAt=08-27 15:13:03.563 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=199 effectiveUid=10292 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity } baseActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} topActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} origActivity=null realActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} numActivities=1 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{6210e31 Task{4b62745 #199 type=standard A=10292:com.ffcs.henan.app}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 128 - 0, 0) topActivityInfo=ActivityInfo{94c2916 com.sk.oa.ui.activity.SplashActivity} launchCookies=[android.os.BinderProxy@6f66297] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(276, 692 - 804, 1772) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@bc2c84, appThread = android.app.IApplicationThread$Stub$Proxy@7c75c6d, debugName = QuickstepLaunch }, displayChange = null, flags = 0, debugId = 344 }
2025-08-27 15:13:03.607  1362-1693  WindowManager           system_server                        V      startWCT=WindowContainerTransaction { changes= {} hops= [] errorCallbackToken=null taskFragmentOrganizer=null }
2025-08-27 15:13:03.607  1362-1693  WindowManager           system_server                        V      info={id=344 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {WCT{RemoteToken{6210e31 Task{4b62745 #199 type=standard A=10292:com.ffcs.henan.app}}} m=OPEN f=NONE leash=Surface(name=Task=199)/@0x42525bb sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
                                                                                                            {WCT{RemoteToken{8d04778 Task{3336d1b #1 type=home}}} m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0x77f3573 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}
                                                                                                        ]}
2025-08-27 15:13:03.607  1997-2593  WindowManagerShell      com.android.systemui                 V  onTransitionReady(transaction=5849745478448)
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V  onTransitionReady (#344) android.os.BinderProxy@dcd1c5e: {id=344 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {m=OPEN f=NONE leash=Surface(name=Task=199)/@0x6d97be6 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
                                                                                                            {m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0x83e0527 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}
                                                                                                        ]}
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V  Playing animation for (#344) android.os.BinderProxy@dcd1c5e@0
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V   try firstHandler com.android.wm.shell.transition.DefaultMixedHandler@5cc233e
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V  Mixed transition for opening an intent with a remote transition and PIP or Desktop #344
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V  tryAnimateOpenIntentWithRemoteAndPipOrDesktop
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V   Delegate animation for (#344) to RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@4aa06a, appThread = android.app.IApplicationThread$Stub$Proxy@8a2c95b, debugName = QuickstepLaunch }
2025-08-27 15:13:03.607  1997-2022  WindowManagerShell      com.android.systemui                 V   animated by firstHandler
2025-08-27 15:13:03.609  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.609  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.609  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.610  1362-3749  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:03.611  1362-1817  ActivityManager         system_server                        D  sync unfroze 20067 com.google.android.settings.intelligence for 6
2025-08-27 15:13:03.616  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.619  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.619  1362-3749  JobInfo                 system_server                        W  Job 'com.google.android.settings.intelligence/.modules.routines.impl.RoutinesJobIntentService#1728' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
2025-08-27 15:13:03.636  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.636  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.637  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.642   579-625   hwc-display             and...graphics.composer@2.4-service  E  [PrimaryDisplay] resetColorMappingInfoForClientComp:: resetColorMappingInfo() idx=1 error(-22)
2025-08-27 15:13:03.642   579-625   hwc-display             and...graphics.composer@2.4-service  E  Fail to open file /data/vendor/log/hwc/PrimaryDisplay_hwc_error_log0.txt, error: Permission denied
2025-08-27 15:13:03.642   579-625   hwc-display             and...graphics.composer@2.4-service  E  Fail to open file /data/log/PrimaryDisplay_hwc_error_log0.txt, error: No such file or directory
2025-08-27 15:13:03.642   579-625   hwc-display             and...graphics.composer@2.4-service  E  Unable to open log file for PrimaryDisplay_hwc_error_log0.txt
2025-08-27 15:13:03.638   579-579   HwBinder:579_1          and...graphics.composer@2.4-service  W  type=1400 audit(0.0:9749): avc:  denied  { search } for  name="log" dev="dm-52" ino=375 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:vendor_log_file:s0 tclass=dir permissive=0
2025-08-27 15:13:03.638   579-579   HwBinder:579_1          and...graphics.composer@2.4-service  W  type=1400 audit(0.0:9750): avc:  denied  { search } for  name="log" dev="dm-52" ino=375 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:vendor_log_file:s0 tclass=dir permissive=0
2025-08-27 15:13:03.652  1362-3749  ConnectivityService     system_server                        D  requestNetwork for uid/pid:10292/6094 activeRequest: null callbackRequest: 2321 [NetworkRequest [ REQUEST id=2322, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10292 RequestorUid: 10292 RequestorPkg: com.ffcs.henan.app UnderlyingNetworks: Null] ]] callback flags: 0 order: 2147483647 isUidTracked: false declaredMethods: AVAIL|LOST|NC|LP
2025-08-27 15:13:03.653  1362-1892  WifiNetworkFactory      system_server                        D  got request NetworkRequest [ REQUEST id=2322, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10292 RequestorUid: 10292 RequestorPkg: com.ffcs.henan.app UnderlyingNetworks: Null] ]
2025-08-27 15:13:03.653  1362-1892  UntrustedW...orkFactory system_server                        D  got request NetworkRequest [ REQUEST id=2322, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10292 RequestorUid: 10292 RequestorPkg: com.ffcs.henan.app UnderlyingNetworks: Null] ]
2025-08-27 15:13:03.653  1362-1892  OemPaidWif...orkFactory system_server                        D  got request NetworkRequest [ REQUEST id=2322, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10292 RequestorUid: 10292 RequestorPkg: com.ffcs.henan.app UnderlyingNetworks: Null] ]
2025-08-27 15:13:03.653  1362-1892  MultiInter...orkFactory system_server                        D  got request NetworkRequest [ REQUEST id=2322, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10292 RequestorUid: 10292 RequestorPkg: com.ffcs.henan.app UnderlyingNetworks: Null] ]
2025-08-27 15:13:03.653  1362-1908  ConnectivityService     system_server                        D  NetReassign [2322 : null 鈫� 135] [c 0] [a 0] [i 1]
2025-08-27 15:13:03.655  1362-1908  ConnectivityService     system_server                        D  NetReassign [no changes] [c 0] [a 0] [i 1]
2025-08-27 15:13:03.667  1362-3749  Grammatica...ctionUtils system_server                        V  AttributionSource: android.content.AttributionSource@4fd544ad does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
2025-08-27 15:13:03.667  1362-3749  OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_RESIZEABLE_ACTIVITY_OVERRIDES
2025-08-27 15:13:03.667  1362-3749  OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_MIN_ASPECT_RATIO_OVERRIDE
2025-08-27 15:13:03.668  1362-3749  OptProp                 system_server                        W  Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_ORIENTATION_OVERRIDE
2025-08-27 15:13:03.668   576-622   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
2025-08-27 15:13:03.669  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.670  1997-2022  WindowManagerShell      com.android.systemui                 V  Transition requested (#345): android.os.BinderProxy@7fbccc3 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=199 effectiveUid=10292 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity } baseActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} topActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity} origActivity=null realActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} numActivities=2 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@6bc940} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{cb66079 com.sk.oa.ui.activity.LoginActivity} launchCookies=[android.os.BinderProxy@3c43e55] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(276, 692 - 804, 1772) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 345 }
2025-08-27 15:13:03.670  1997-2022  ShellSplitScreen        com.android.systemui                 D  logExit: no-op, mLoggerSessionId is null
2025-08-27 15:13:03.671  1362-3749  ActivityTaskManager     system_server                        I  START u0 {flg=0x10000000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity (has extras)} with LAUNCH_SINGLE_TASK from uid 10292 (sr=87980988) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
2025-08-27 15:13:03.677  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.678  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.678  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:03.678  1997-2668  WindowManagerShell      com.android.systemui                 V  onTransitionReady(transaction=5849745478457)
2025-08-27 15:13:03.679  1997-2022  WindowManagerShell      com.android.systemui                 V  onTransitionReady (#345) android.os.BinderProxy@7fbccc3: {id=345 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {m=OPEN f=STARTING_WINDOW_TRANSFER|FILLS_TASK|IS_BEHIND_STARTING_WINDOW leash=Surface(name=ActivityRecord{189120180 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity)/@0x1a221be sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=fffafafa component=com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity},
                                                                                                            {m=CLOSE f=FILLS_TASK|IS_BEHIND_STARTING_WINDOW|IS_OCCLUDED leash=Surface(name=ActivityRecord{87980988 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity)/@0xf44721f sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=fffafafa component=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity}
                                                                                                        ]}
2025-08-27 15:13:03.679  1362-1693  WindowManager           system_server                        V  Sent Transition (#345) createdAt=08-27 15:13:03.667 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=199 effectiveUid=10292 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity } baseActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} topActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity} origActivity=null realActivity=ComponentInfo{com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity} numActivities=2 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{6210e31 Task{4b62745 #199 type=standard A=10292:com.ffcs.henan.app}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{3a40538 com.sk.oa.ui.activity.LoginActivity} launchCookies=[android.os.BinderProxy@6f66297] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(276, 692 - 804, 1772) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 345 }
2025-08-27 15:13:03.679  1997-2022  WindowManagerShell      com.android.systemui                 V  Non-visible anim so abort: (#345) android.os.BinderProxy@7fbccc3@0
2025-08-27 15:13:03.679  1362-1693  WindowManager           system_server                        V      startWCT=WindowContainerTransaction { changes= {} hops= [] errorCallbackToken=null taskFragmentOrganizer=null }
2025-08-27 15:13:03.679  1997-2022  WindowManagerShell      com.android.systemui                 V  Transition was merged: (#345) android.os.BinderProxy@7fbccc3@0 into (#344) android.os.BinderProxy@dcd1c5e@0
2025-08-27 15:13:03.679  1362-1693  WindowManager           system_server                        V      info={id=345 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {m=OPEN f=STARTING_WINDOW_TRANSFER|FILLS_TASK|IS_BEHIND_STARTING_WINDOW leash=Surface(name=ActivityRecord{189120180 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity)/@0xce39daa sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=fffafafa component=com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity},
                                                                                                            {m=CLOSE f=FILLS_TASK|IS_BEHIND_STARTING_WINDOW|IS_OCCLUDED leash=Surface(name=ActivityRecord{87980988 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity)/@0xfd9e895 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=fffafafa component=com.ffcs.henan.app/com.sk.oa.ui.activity.SplashActivity}
                                                                                                        ]}
2025-08-27 15:13:03.679  1362-2968  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:03.689  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.689  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.690  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.690  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.697  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.698  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.698  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.698  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.698  6094-6094  HWUI                    com.ffcs.henan.app                   W  Image decoding logging dropped!
2025-08-27 15:13:03.703  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.704  1362-13116 Grammatica...ctionUtils system_server                        V  AttributionSource: android.content.AttributionSource@4fd544ad does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
2025-08-27 15:13:03.705  1362-13116 CompatChangeReporter    system_server                        D  Compat change id reported: 135754954; UID 10292; state: ENABLED
2025-08-27 15:13:03.705  1362-1712  CompatChangeReporter    system_server                        D  Compat change id reported: 143937733; UID 10292; state: ENABLED
2025-08-27 15:13:03.711  1362-3749  CoreBackPreview         system_server                        D  Window{ab5ea76 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@58ea714, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
2025-08-27 15:13:03.715   865-865   Zygote                  zygote64                             D  Forked child process 6462
2025-08-27 15:13:03.715  1362-1712  ActivityManager         system_server                        I  Start proc 6462:com.ffcs.henan.app:bg/u0a292 for service {com.ffcs.henan.app/com.sdp.sase.core.bg.SaseVpnService}
2025-08-27 15:13:03.724  1362-13116 CoreBackPreview         system_server                        D  Window{5cbdd26 u0 com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@dc6225f, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
2025-08-27 15:13:03.725  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.728  1362-1693  ActivityTaskManager     system_server                        I  Displayed com.ffcs.henan.app/com.sk.oa.ui.activity.LoginActivity for user 0: +163ms
2025-08-27 15:13:03.730  6462-6462  cs.henan.app:bg         zygote64                             I  Using CollectorTypeCMC GC.
2025-08-27 15:13:03.733  6462-6462  cs.henan.app:bg         zygote64                             E  Not starting debugger since process cannot load the jdwp agent.
2025-08-27 15:13:03.734  1362-13116 AutofillSession         system_server                        V  Primary service component name: ComponentInfo{com.google.android.gms/com.google.android.gms.autofill.service.AutofillService}, secondary service component name: ComponentInfo{com.android.credentialmanager/com.android.credentialmanager.autofill.CredentialAutofillService}
2025-08-27 15:13:03.735  1362-13116 SecondaryP...derHandler system_server                        V  Creating a secondary provider handler with component name, ComponentInfo{com.android.credentialmanager/com.android.credentialmanager.autofill.CredentialAutofillService}
2025-08-27 15:13:03.735  1362-13116 Presentati...ventLogger system_server                        D  Started new PresentationStatsEvent
2025-08-27 15:13:03.747  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.752  6462-6462  nativeloader            com.ffcs.henan.app:bg                D  Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
2025-08-27 15:13:03.755  1362-2968  AutofillSession         system_server                        D  createPendingIntent for request 960
2025-08-27 15:13:03.764  6462-6462  ApplicationLoaders      com.ffcs.henan.app:bg                D  Returning zygote-cached class loader: /system/framework/org.apache.http.legacy.jar
2025-08-27 15:13:03.766  6462-6462  nativeloader            com.ffcs.henan.app:bg                D  Configuring clns-7 for other apk /data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/base.apk. target_sdk_version=30, uses_libraries=, library_path=/data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/lib/arm64:/data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.ffcs.henan.app
2025-08-27 15:13:03.768  6462-6462  LoadedApk               com.ffcs.henan.app:bg                E  Unable to instantiate appComponentFactory
                                                                                                    java.lang.ClassNotFoundException: Didn't find class "androidx.core.app.CoreComponentFactory" on path: DexPathList[[zip file "/data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/base.apk"],nativeLibraryDirectories=[/data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/lib/arm64, /data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/base.apk!/lib/arm64-v8a, /system/lib64, /system_ext/lib64]]
                                                                                                    	at dalvik.system.BaseDexClassLoader.findClass(BaseDexClassLoader.java:259)
                                                                                                    	at java.lang.ClassLoader.loadClass(ClassLoader.java:637)
                                                                                                    	at java.lang.ClassLoader.loadClass(ClassLoader.java:573)
                                                                                                    	at android.app.LoadedApk.createAppFactory(LoadedApk.java:272)
                                                                                                    	at android.app.LoadedApk.createOrUpdateClassLoaderLocked(LoadedApk.java:1043)
                                                                                                    	at android.app.LoadedApk.getClassLoader(LoadedApk.java:1134)
                                                                                                    	at android.app.LoadedApk.getResources(LoadedApk.java:1382)
                                                                                                    	at android.app.ContextImpl.createAppContext(ContextImpl.java:3489)
                                                                                                    	at android.app.ContextImpl.createAppContext(ContextImpl.java:3480)
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7575)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2500)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:109)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:232)
                                                                                                    	at android.os.Looper.loop(Looper.java:317)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8934)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:591)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:911)
2025-08-27 15:13:03.769  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.773  6462-6462  cs.henan.app:bg         com.ffcs.henan.app:bg                I  AssetManager2(0xb4000076d3d19a18) locale list changing from [] to [zh-Hans-CN]
2025-08-27 15:13:03.775  6462-6462  GraphicsEnvironment     com.ffcs.henan.app:bg                V  Currently set values for:
2025-08-27 15:13:03.775  6462-6462  GraphicsEnvironment     com.ffcs.henan.app:bg                V    angle_gl_driver_selection_pkgs=[com.android.angle, com.linecorp.b612.android, com.campmobile.snow, com.google.android.apps.tachyon]
2025-08-27 15:13:03.775  6462-6462  GraphicsEnvironment     com.ffcs.henan.app:bg                V    angle_gl_driver_selection_values=[angle, native, native, native]
2025-08-27 15:13:03.775  6462-6462  GraphicsEnvironment     com.ffcs.henan.app:bg                V  com.ffcs.henan.app is not listed in per-application setting
2025-08-27 15:13:03.775  6462-6462  GraphicsEnvironment     com.ffcs.henan.app:bg                V  Neither updatable production driver nor prerelease driver is supported.
2025-08-27 15:13:03.774  6462-6462  cs.henan.app:bg         com.ffcs.henan.app:bg                W  type=1400 audit(0.0:9751): avc:  denied  { read } for  name="tcp" dev="proc" ino=4026532026 scontext=u:r:untrusted_app_30:s0:c36,c257,c512,c768 tcontext=u:object_r:proc_net_tcp_udp:s0 tclass=file permissive=0 app=com.ffcs.henan.app
2025-08-27 15:13:03.781  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.782  6462-6462  nativeloader            com.ffcs.henan.app:bg                D  Load /data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/lib/arm64/libegis.so using ns clns-7 from class loader (caller=/data/app/~~9WU5dVRhc6EkROBplmlvBw==/com.ffcs.henan.app-YyQ2gkEXkN4tW--tJlUv4w==/base.apk): ok
2025-08-27 15:13:03.778  6462-6462  cs.henan.app:bg         com.ffcs.henan.app:bg                W  type=1400 audit(0.0:9752): avc:  denied  { read } for  name="tcp" dev="proc" ino=4026532026 scontext=u:r:untrusted_app_30:s0:c36,c257,c512,c768 tcontext=u:object_r:proc_net_tcp_udp:s0 tclass=file permissive=0 app=com.ffcs.henan.app
2025-08-27 15:13:03.778  6462-6462  cs.henan.app:bg         com.ffcs.henan.app:bg                W  type=1400 audit(0.0:9753): avc:  denied  { read } for  name="tcp" dev="proc" ino=4026532026 scontext=u:r:untrusted_app_30:s0:c36,c257,c512,c768 tcontext=u:object_r:proc_net_tcp_udp:s0 tclass=file permissive=0 app=com.ffcs.henan.app
2025-08-27 15:13:03.778  6462-6462  cs.henan.app:bg         com.ffcs.henan.app:bg                W  type=1400 audit(0.0:9754): avc:  denied  { read } for  name="tcp" dev="proc" ino=4026532026 scontext=u:r:untrusted_app_30:s0:c36,c257,c512,c768 tcontext=u:object_r:proc_net_tcp_udp:s0 tclass=file permissive=0 app=com.ffcs.henan.app
2025-08-27 15:13:03.792  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.803   976-1582  audioserver             audioserver                          D  logFgsApiEnd: FGS Logger Transaction failed, -129
2025-08-27 15:13:03.804   882-906   audio_hw_waves          android.hardware.audio.service       D  update_sink_info_from_usecases: update amcs, instance id 1, volume 227 0 0 0 0 0 0 0 0 0
2025-08-27 15:13:03.804   882-906   audio_hw_35l41          android.hardware.audio.service       W  cs35l41_amp_common_event: ret: 0, event: 15, state: 3, device: 0
2025-08-27 15:13:03.804   882-906   audio_hw_output_stream  android.hardware.audio.service       D  update stream 1 active 0 gain 0.000000
2025-08-27 15:13:03.814  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.825  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.836  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.840  2817-3294  AiAiStatsd              com.google.android.as                I  (REDACTED) Successfully logged feature usage for %s.
2025-08-27 15:13:03.840  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.facebook.katana, user=UserHandle{0}
2025-08-27 15:13:03.840  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.whatsapp, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.instagram.android, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.facebook.orca, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=jp.naver.line.android, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.snapchat.android, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.microsoft.office.outlook, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.spotify.music, user=UserHandle{0}
2025-08-27 15:13:03.841  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.twitter.android, user=UserHandle{0}
2025-08-27 15:13:03.842  2817-3257  LauncherApps            com.google.android.as                D  getActivityList: No launchable activities found forpackageName=com.zhiliaoapp.musically, user=UserHandle{0}
2025-08-27 15:13:03.848  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.858  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.881  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.893  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.903  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.914  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.926  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.936  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.958  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.969  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:03.981  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.003  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.014  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.036  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.058  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.065  6462-6462  Process                 com.ffcs.henan.app:bg                I  Sending signal. PID: 6462 SIG: 9
2025-08-27 15:13:04.079  1362-3749  ActivityManager         system_server                        I  Process com.ffcs.henan.app:bg (pid 6462) has died: fg  CEM 
2025-08-27 15:13:04.079   865-865   Zygote                  zygote64                             I  Process 6462 exited due to signal 9 (Killed)
2025-08-27 15:13:04.080  1362-1729  libprocessgroup         system_server                        I  Removed cgroup /sys/fs/cgroup/uid_10292/pid_6462
2025-08-27 15:13:04.103  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.125  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.126  1997-2022  WindowManagerShell      com.android.systemui                 V  Transition animation finished (aborted=false), notifying core (#344) android.os.BinderProxy@dcd1c5e@0
2025-08-27 15:13:04.127  1362-1693  WindowManager           system_server                        V  Finish Transition (#344): created at 08-27 15:13:03.563 collect-started=0.078ms request-sent=9.679ms started=11.632ms ready=23.272ms sent=42.794ms finished=563.817ms
2025-08-27 15:13:04.128  1362-1693  WindowManager           system_server                        V  Finish Transition (#345): created at 08-27 15:13:03.667 collect-started=0.031ms request-sent=2.467ms started=4.51ms ready=9.42ms sent=10.646ms finished=461.208ms
2025-08-27 15:13:04.129  1997-2022  WindowManagerShell      com.android.systemui                 V  Track 0 became idle
2025-08-27 15:13:04.129  1997-2022  WindowManagerShell      com.android.systemui                 V  All active transition animations finished
2025-08-27 15:13:04.129  1362-3749  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:04.130  1362-3749  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:04.130  1362-3749  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:04.130  2873-2912  ForegroundUtils         com.android.nfc                      D  Foreground UID status:
2025-08-27 15:13:04.130  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10079 is in foreground: false
2025-08-27 15:13:04.130  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10091 is in foreground: false
2025-08-27 15:13:04.130  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10122 is in foreground: true
2025-08-27 15:13:04.130  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10169 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10203 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10214 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10238 is in foreground: true
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10260 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10264 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10268 is in foreground: true
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10273 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10280 is in foreground: false
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10292 is in foreground: true
2025-08-27 15:13:04.131  2873-2912  ForegroundUtils         com.android.nfc                      D  UID: 10293 is in foreground: false
2025-08-27 15:13:04.132  1362-3749  UserManagerService      system_server                        W  Requested status bar icon for non-badged user 0
2025-08-27 15:13:04.137  6094-6094  .ffcs.henan.app         com.ffcs.henan.app                   W  Accessing hidden field Landroid/view/inputmethod/InputMethodManager;->mCurRootView:Landroid/view/ViewRootImpl; (blocked, reflection, denied)
2025-08-27 15:13:04.137  6094-6094  .ffcs.henan.app         com.ffcs.henan.app                   W  Accessing hidden field Landroid/view/inputmethod/InputMethodManager;->mServedView:Landroid/view/View; (blocked, reflection, denied)
2025-08-27 15:13:04.137  6094-6094  .ffcs.henan.app         com.ffcs.henan.app                   W  Accessing hidden field Landroid/view/inputmethod/InputMethodManager;->mNextServedView:Landroid/view/View; (blocked, reflection, denied)
2025-08-27 15:13:04.139  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:04.139  2367-2367  VRI[NexusL...rActivity] com...le.android.apps.nexuslauncher  D  visibilityChanged oldVisibility=true newVisibility=false
2025-08-27 15:13:04.141  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  setSurface:
                                                                                                    	mWaitingOnSurfaceValidity: false
                                                                                                    	mSurface: null
2025-08-27 15:13:04.141  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.141  2367-2367  StatsLog                com...le.android.apps.nexuslauncher  D  LAUNCHER_ONSTOP
2025-08-27 15:13:04.141  2367-3348  GmsPlayProvider         com...le.android.apps.nexuslauncher  D  Shut down the binder channel
2025-08-27 15:13:04.141  2367-2367  StatsLog                com...le.android.apps.nexuslauncher  D  LAUNCHER_GOOGLE_SEARCH_RESTORE_LIST_SIZE_AFTER_ACTIVITY_RESTART
2025-08-27 15:13:04.141  2367-2367  NexusLaunc...elDelegate com...le.android.apps.nexuslauncher  D  notifySmartspaceEvent: SmartspaceTargetEvent{mSmartspaceTarget=null, mSmartspaceActionId='null', mEventType=7}
2025-08-27 15:13:04.142  2367-2367  BaseDepthController     com...le.android.apps.nexuslauncher  D  mSurface is null and mCurrentBlur is: 0
2025-08-27 15:13:04.142  2367-2974  IPCThreadState          com...le.android.apps.nexuslauncher  I  oneway function results for code 2 on binder at 0xb400007773d71bf0 will be dropped but finished with status UNKNOWN_TRANSACTION
2025-08-27 15:13:04.144 19136-19136 PlayCloudSearchChimeraS com.google.android.gms.persistent    I  Stopping the server
2025-08-27 15:13:04.144 19136-19136 PlayCloudSearchLifeCycl com.google.android.gms.persistent    I  Stopping the GRPC server
2025-08-27 15:13:04.147  2367-2367  NexusLaunc...elDelegate com...le.android.apps.nexuslauncher  D  notifySmartspaceEvent: SmartspaceTargetEvent{mSmartspaceTarget=null, mSmartspaceActionId='null', mEventType=7}
2025-08-27 15:13:04.151  1362-1958  Background...rolService system_server                        D  Package event received: 0
2025-08-27 15:13:04.495  1997-2033  WindowOnBackDispatcher  com.android.systemui                 W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@bb75f2f
2025-08-27 15:13:04.495  1362-13116 CoreBackPreview         system_server                        D  Window{f4cd143 u0 Splash Screen com.ffcs.henan.app EXITING}: Setting back callback null
2025-08-27 15:13:04.624   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_rtlogger_data_analysis: Left/Top impedance: 577777
2025-08-27 15:13:04.624   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_rtlogger_data_analysis: Right/Bottom impedance: 666578
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Left/Top Max temp: 0x826fa ==> 32.609009 C
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Left/Top Max exc: 0x13c88 ==> 0.154556 mm
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Left/Top dsp heartbeat = 0x275ed
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Right/Bottom Max temp: 0x818b4 ==> 32.385986 C
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Right/Bottom Max exc: 0x1c9d0 ==> 0.223541 mm
2025-08-27 15:13:04.630   882-1431  audio_hw_35l41          android.hardware.audio.service       D  cs35l41_bdlogger_data_analysis: Right/Bottom dsp heartbeat = 0x275e2
2025-08-27 15:13:05.356   968-4046  WifiHAL                 ven...oogle.wifi_ext-service-vendor  I  Creating message to get link statistics; iface = 47
2025-08-27 15:13:05.386   894-935   CHRE                    and...re.contexthub-service.generic  I  @ 1037976.412: [AR] inconsistent: 41.176471
2025-08-27 15:13:05.386   894-935   CHRE                    and...re.contexthub-service.generic  D  @ 1037976.413: [ActivityPlatform] type 6, confidence 41
2025-08-27 15:13:05.432  2561-2725  IwlanNetworkService     com.google.android.iwlan             D  onCapabilitiesChanged: 135
2025-08-27 15:13:05.432  2561-2711  IwlanDataService        com.google.android.iwlan             D  onCapabilitiesChanged: 135 [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: "PayEgis-02", BSSID: 18:6f:2d:42:56:53, MAC: 74:74:46:bd:5d:75, IP: /**************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ax, RSSI: -68, Link speed: 103Mbps, Tx Link speed: 103Mbps, Max Supported Tx Link speed: 286Mbps, Rx Link speed: 286Mbps, Max Supported Rx Link speed: 286Mbps, Frequency: 5180MHz, Net ID: 0, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: 1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none>"PayEgis-02"wpa2-pskMLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -68 AdminUids: [10134] SSID: "PayEgis-02" UnderlyingNetworks: Null]
2025-08-27 15:13:05.432  2561-2711  IwlanDataService        com.google.android.iwlan             D  Network 135 connected using transport WIFI
2025-08-27 15:13:05.433  3863-4012  DeviceStateHelper       com.google.android.apps.scone        D  Wifi freq: 5180
2025-08-27 15:13:05.434  2844-3381  BugleRcsEngine          com...le.android.apps.messaging:rcs  I  Connected state: [1], networkType: [WIFI] [CONTEXT thread_id=54 ]
2025-08-27 15:13:05.435 19136-19232 NullBinder              com.google.android.gms.persistent    I  NullBinder for android.net.action.RECOMMEND_NETWORKS triggering remote TransactionTooLargeException due to Service without Chimera impl, calling uid: 1000, calling pid: 0
2025-08-27 15:13:05.435 19136-19232 .gms.persistent         com.google.android.gms.persistent    W  Large reply transaction of 1056768 bytes, interface descriptor , code 1
2025-08-27 15:13:05.435  1362-1908  ConnectivityService     system_server                        D  NetReassign [no changes] [c 0] [a 0] [i 1]
2025-08-27 15:13:05.436  2844-3447  BugleRcsEngine          com...le.android.apps.messaging:rcs  I  handleMessage processing message:[NOTIFY_UPTIME_IGNORE_STATE_CHANGED] with [non-null]:RcsEngineImpl reference [CONTEXT log_prefix="RcsEngineImpl[DUAL_REG]:[64e3c3f9-ffd6]>Handler" thread_id=61 ]
2025-08-27 15:13:05.437  2254-2364  PhoneInterfaceManager   com.android.phone                    E  getCarrierPrivilegeStatusForUid: Invalid subId
2025-08-27 15:13:05.438  2844-3447  BugleRcsEngine          com...le.android.apps.messaging:rcs  I  RCS Configuration storage in Bugle is disabled by p/h flag, using ProvisioningEngineStateCache.getProvisioningConfigurationForGivenSimIfExist() [CONTEXT log_prefix="ProvisioningEngineDataRetriever" thread_id=61 ]
