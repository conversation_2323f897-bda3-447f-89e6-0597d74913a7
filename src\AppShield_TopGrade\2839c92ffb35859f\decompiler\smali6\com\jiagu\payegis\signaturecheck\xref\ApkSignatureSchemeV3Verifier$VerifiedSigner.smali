.class public Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;
.super Ljava/lang/Object;
.source "ApkSignatureSchemeV3Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "VerifiedSigner"
.end annotation


# instance fields
.field public final certs:[Ljava/security/cert/X509Certificate;

.field public final por:Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;

.field public verityRootHash:[B


# direct methods
.method public constructor <init>([Ljava/security/cert/X509Certificate;Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;)V
    .registers 3

    .prologue
    .line 499
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 500
    iput-object p1, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;->certs:[Ljava/security/cert/X509Certificate;

    .line 501
    iput-object p2, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;->por:Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;

    .line 502
    return-void
.end method
