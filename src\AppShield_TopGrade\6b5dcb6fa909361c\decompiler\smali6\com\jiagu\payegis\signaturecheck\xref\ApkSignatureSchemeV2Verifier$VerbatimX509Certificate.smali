.class Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$VerbatimX509Certificate;
.super Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;
.source "ApkSignatureSchemeV2Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "VerbatimX509Certificate"
.end annotation


# instance fields
.field private encodedVerbatim:[B


# direct methods
.method public constructor <init>(Ljava/security/cert/X509Certificate;[B)V
    .registers 3

    .prologue
    .line 757
    invoke-direct {p0, p1}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$WrappedX509Certificate;-><init>(Ljava/security/cert/X509Certificate;)V

    .line 758
    iput-object p2, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$VerbatimX509Certificate;->encodedVerbatim:[B

    .line 759
    return-void
.end method


# virtual methods
.method public getEncoded()[B
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/cert/CertificateEncodingException;
        }
    .end annotation

    .prologue
    .line 763
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$VerbatimX509Certificate;->encodedVerbatim:[B

    return-object v0
.end method
