.class final Lcom/jiagu/payegis/utils/ToastUtil$1;
.super Ljava/util/TimerTask;
.source "ToastUtil.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$context:Landroid/content/Context;


# direct methods
.method constructor <init>(Landroid/content/Context;)V
    .registers 2

    .prologue
    .line 86
    iput-object p1, p0, Lcom/jiagu/payegis/utils/ToastUtil$1;->val$context:Landroid/content/Context;

    invoke-direct {p0}, Ljava/util/TimerTask;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .registers 8

    .prologue
    const/4 v6, 0x0

    .line 89
    new-instance v0, Ljava/util/Timer;

    invoke-direct {v0}, Ljava/util/Timer;-><init>()V

    # setter for: Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;
    invoke-static {v0}, Lcom/jiagu/payegis/utils/ToastUtil;->access$002(Ljava/util/Timer;)Ljava/util/Timer;

    .line 90
    const/16 v4, 0xbb8

    .line 91
    new-instance v1, Lcom/jiagu/payegis/utils/ToastUtil$1$1;

    invoke-direct {v1, p0}, Lcom/jiagu/payegis/utils/ToastUtil$1$1;-><init>(Lcom/jiagu/payegis/utils/ToastUtil$1;)V

    .line 102
    # getter for: Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;
    invoke-static {}, Lcom/jiagu/payegis/utils/ToastUtil;->access$000()Ljava/util/Timer;

    move-result-object v0

    const-wide/16 v2, 0xa

    int-to-long v4, v4

    invoke-virtual/range {v0 .. v5}, Ljava/util/Timer;->scheduleAtFixedRate(Ljava/util/TimerTask;JJ)V

    .line 104
    # setter for: Lcom/jiagu/payegis/utils/ToastUtil;->timer:Ljava/util/Timer;
    invoke-static {v6}, Lcom/jiagu/payegis/utils/ToastUtil;->access$302(Ljava/util/Timer;)Ljava/util/Timer;

    .line 105
    # setter for: Lcom/jiagu/payegis/utils/ToastUtil;->timer2:Ljava/util/Timer;
    invoke-static {v6}, Lcom/jiagu/payegis/utils/ToastUtil;->access$002(Ljava/util/Timer;)Ljava/util/Timer;

    .line 106
    return-void
.end method
