// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0xbe, 0xdc, 0xf2, 0xa5, 0x5d, 0x0a, 0x65, 0x50, 0x4f, 0x7a, 0xf0, 0x60, 0xca, 0x7f, 0x74, 0x40, 0x41, 0x6f, 0xf8, 0x27, 0x3c, 0x13, 0xd0, 0x2a, 0xa8, 0x96, 0xba, 0xc1, 0x31, 0x38, 0x76, 0x53, 0x08, 0x3a, 0x43, 0x2f, 0x09, 0x2e, 0x66, 0x26, 0xc6, 0x36, 0xf4, 0xcf, 0x83, 0xbf, 0x7b, 0xcd, 0xa6, 0xbd, 0x1d, 0x48, 0xc8, 0x3f, 0x81, 0x5c, 0x6c, 0xb5, 0x89, 0xdd, 0x1a, 0xf7, 0x97, 0xd3, 0xf6, 0xee, 0x12, 0xfa, 0x0e, 0x88, 0x8b, 0x30, 0xae, 0xaa, 0xa1, 0x18, 0x52, 0x9b, 0x1e, 0x05, 0xc5, 0x06, 0xc3, 0x10, 0x98, 0x0b, 0x1f, 0x29, 0xda, 0xb8, 0xb6, 0xe1, 0xe6, 0x7d, 0xb9, 0x2c, 0x9d, 0x90, 0x94, 0x57, 0x5f, 0x7e, 0xed, 0xe3, 0xec, 0x1b, 0x7c, 0x55, 0x92, 0xd1, 0x6a, 0xf5, 0x33, 0x35, 0x9c, 0x46, 0x1c, 0x86, 0xe4, 0x6b, 0x95, 0x32, 0x70, 0x73, 0x19, 0x0f, 0xab, 0xc4, 0x93, 0x9a, 0x8e, 0x4c, 0xe7, 0x3e, 0x3b, 0x15, 0xdb, 0x5b, 0x0c, 0xde, 0x45, 0x03, 0xd5, 0xe2, 0x28, 0x9f, 0xfd, 0x99, 0x37, 0x87, 0xf1, 0x20, 0xc2, 0x22, 0x71, 0x47, 0x91, 0x02, 0x80, 0x61, 0x51, 0xea, 0x82, 0x3d, 0x78, 0x6e, 0x0d, 0x42, 0xfb, 0xb7, 0x8d, 0x14, 0x8a, 0x16, 0x68, 0xe5, 0xd7, 0x24, 0xa0, 0x59, 0xac, 0x69, 0xeb, 0x5a, 0xf3, 0x58, 0x85, 0xe0, 0x6d, 0xce, 0xfc, 0x49, 0xff, 0x5e, 0xd8, 0xa4, 0x67, 0x4b, 0x8c, 0xa7, 0x04, 0x64, 0xa3, 0x01, 0x62, 0x4d, 0x34, 0x77, 0xb2, 0x23, 0xd4, 0xa9, 0x17, 0x11, 0xad, 0xe8, 0xb4, 0xaf, 0xc9, 0xb0, 0x63, 0xef, 0xd2, 0x44, 0x4e, 0xbc, 0x9e, 0x2b, 0x07, 0x75, 0x56, 0x79, 0xc7, 0xcc, 0x8f, 0x39, 0x00, 0xd9, 0xbb, 0xfe, 0xe9, 0x72, 0xd6, 0x4a, 0xdf, 0xcb, 0xf9, 0x2d, 0x84, 0x21, 0xa2, 0xc0, 0xb3, 0x25, 0xb1, 0x54};
#endif
