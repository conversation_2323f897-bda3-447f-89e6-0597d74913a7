.class Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;
.super Ljava/lang/Object;
.source "ApkSignatureSchemeV2Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "SignatureInfo"
.end annotation


# instance fields
.field private final apkSigningBlockOffset:J

.field private final centralDirOffset:J

.field private final eocd:Ljava/nio/ByteBuffer;

.field private final eocdOffset:J

.field private final signatureBlock:Ljava/nio/ByteBuffer;


# direct methods
.method private constructor <init>(Ljava/nio/ByteBuffer;JJJLjava/nio/ByteBuffer;)V
    .registers 9

    .prologue
    .line 136
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 137
    iput-object p1, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->signatureBlock:Ljava/nio/ByteBuffer;

    .line 138
    iput-wide p2, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->apkSigningBlockOffset:J

    .line 139
    iput-wide p4, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->centralDirOffset:J

    .line 140
    iput-wide p6, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->eocdOffset:J

    .line 141
    iput-object p8, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->eocd:Ljava/nio/ByteBuffer;

    .line 142
    return-void
.end method

.method synthetic constructor <init>(Ljava/nio/ByteBuffer;JJJLjava/nio/ByteBuffer;Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$1;)V
    .registers 10

    .prologue
    .line 115
    invoke-direct/range {p0 .. p8}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;-><init>(Ljava/nio/ByteBuffer;JJJLjava/nio/ByteBuffer;)V

    return-void
.end method

.method static synthetic access$100(Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;)Ljava/nio/ByteBuffer;
    .registers 2

    .prologue
    .line 115
    iget-object v0, p0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier$SignatureInfo;->signatureBlock:Ljava/nio/ByteBuffer;

    return-object v0
.end method
