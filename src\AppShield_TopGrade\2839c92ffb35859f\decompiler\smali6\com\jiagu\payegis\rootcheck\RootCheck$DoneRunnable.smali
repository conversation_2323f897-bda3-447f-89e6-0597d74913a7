.class public final Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;
.super Ljava/lang/Object;
.source "RootCheck.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/rootcheck/RootCheck;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "DoneRunnable"
.end annotation


# instance fields
.field private final mContext:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference",
            "<",
            "Landroid/content/Context;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method protected constructor <init>(Landroid/content/Context;)V
    .registers 3

    .prologue
    .line 68
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 69
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    .line 70
    return-void
.end method


# virtual methods
.method public run()V
    .registers 3

    .prologue
    .line 74
    invoke-static {}, Lcom/jiagu/payegis/utils/LanguageUtil;->isDomestic()Z

    move-result v0

    if-eqz v0, :cond_14

    .line 75
    iget-object v0, p0, Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v1, "\u624b\u673a\u5df2\u88abroot\uff0c\u5b58\u5728\u4fe1\u606f\u6cc4\u6f0f\u548c\u5b89\u5168\u98ce\u9669\uff0c\u8bf7\u91cd\u65b0\u914d\u7f6e\u64cd\u4f5c\u7cfb\u7edf\u3002"

    invoke-static {v0, v1}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    .line 79
    :goto_13
    return-void

    .line 77
    :cond_14
    iget-object v0, p0, Lcom/jiagu/payegis/rootcheck/RootCheck$DoneRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const-string v1, "This Phone Is Rooted!"

    invoke-static {v0, v1}, Lcom/jiagu/payegis/utils/ToastUtil;->showToast(Landroid/content/Context;Ljava/lang/CharSequence;)V

    goto :goto_13
.end method
