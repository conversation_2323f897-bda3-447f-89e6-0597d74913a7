.class public Lcom/jiagu/payegis/utils/ActivityUtil;
.super Ljava/lang/Object;
.source "ActivityUtil.java"


# static fields
.field private static INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;


# instance fields
.field private handler:Landroid/os/Handler;

.field private isQuit:Z

.field private mContext:Landroid/content/Context;

.field private r:Ljava/lang/Runnable;

.field private toastContent:Ljava/lang/String;


# direct methods
.method private constructor <init>(Landroid/content/Context;)V
    .registers 4

    .prologue
    .line 85
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 21
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->isQuit:Z

    .line 86
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->mContext:Landroid/content/Context;

    .line 87
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->handler:Landroid/os/Handler;

    .line 88
    new-instance v0, Lcom/jiagu/payegis/utils/ActivityUtil$1;

    invoke-direct {v0, p0}, Lcom/jiagu/payegis/utils/ActivityUtil$1;-><init>(Lcom/jiagu/payegis/utils/ActivityUtil;)V

    iput-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->r:Ljava/lang/Runnable;

    .line 96
    return-void
.end method

.method public static getCurrentPkgName(Landroid/content/Context;)Ljava/lang/String;
    .registers 9

    .prologue
    const/4 v2, 0x0

    .line 31
    .line 33
    const/4 v5, 0x2

    .line 37
    :try_start_2
    const-class v0, Landroid/app/ActivityManager$RunningAppProcessInfo;

    const-string v1, "processState"

    invoke-virtual {v0, v1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;
    :try_end_9
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_9} :catch_40

    move-result-object v0

    move-object v1, v0

    .line 41
    :goto_b
    const-string v0, "activity"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 42
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getRunningAppProcesses()Ljava/util/List;

    move-result-object v6

    .line 44
    const/4 v0, 0x0

    move v3, v0

    :goto_19
    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v0

    if-ge v3, v0, :cond_50

    .line 46
    invoke-interface {v6, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager$RunningAppProcessInfo;

    .line 48
    iget v4, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->importance:I

    const/16 v7, 0x64

    if-ne v4, v7, :cond_4c

    .line 51
    :try_start_2b
    invoke-virtual {v1, v0}, Ljava/lang/reflect/Field;->getInt(Ljava/lang/Object;)I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;
    :try_end_32
    .catch Ljava/lang/Exception; {:try_start_2b .. :try_end_32} :catch_46

    move-result-object v4

    .line 56
    :goto_33
    if-eqz v4, :cond_4c

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    if-ne v4, v5, :cond_4c

    .line 62
    :goto_3b
    if-eqz v0, :cond_3f

    .line 63
    iget-object v2, v0, Landroid/app/ActivityManager$RunningAppProcessInfo;->processName:Ljava/lang/String;

    .line 65
    :cond_3f
    return-object v2

    .line 38
    :catch_40
    move-exception v0

    .line 39
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    move-object v1, v2

    goto :goto_b

    .line 52
    :catch_46
    move-exception v4

    .line 53
    invoke-virtual {v4}, Ljava/lang/Exception;->printStackTrace()V

    move-object v4, v2

    goto :goto_33

    .line 44
    :cond_4c
    add-int/lit8 v0, v3, 0x1

    move v3, v0

    goto :goto_19

    :cond_50
    move-object v0, v2

    goto :goto_3b
.end method

.method public static getInstance(Landroid/content/Context;)Lcom/jiagu/payegis/utils/ActivityUtil;
    .registers 3

    .prologue
    .line 99
    sget-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    if-nez v0, :cond_13

    .line 100
    const-class v1, Lcom/jiagu/payegis/utils/ActivityUtil;

    monitor-enter v1

    .line 101
    :try_start_7
    sget-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    if-nez v0, :cond_12

    .line 102
    new-instance v0, Lcom/jiagu/payegis/utils/ActivityUtil;

    invoke-direct {v0, p0}, Lcom/jiagu/payegis/utils/ActivityUtil;-><init>(Landroid/content/Context;)V

    sput-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    .line 104
    :cond_12
    monitor-exit v1
    :try_end_13
    .catchall {:try_start_7 .. :try_end_13} :catchall_24

    .line 106
    :cond_13
    sget-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    iget-object v0, v0, Lcom/jiagu/payegis/utils/ActivityUtil;->mContext:Landroid/content/Context;

    if-nez v0, :cond_21

    .line 107
    sget-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    invoke-virtual {p0}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    iput-object v1, v0, Lcom/jiagu/payegis/utils/ActivityUtil;->mContext:Landroid/content/Context;

    .line 109
    :cond_21
    sget-object v0, Lcom/jiagu/payegis/utils/ActivityUtil;->INSTANCE:Lcom/jiagu/payegis/utils/ActivityUtil;

    return-object v0

    .line 104
    :catchall_24
    move-exception v0

    :try_start_25
    monitor-exit v1
    :try_end_26
    .catchall {:try_start_25 .. :try_end_26} :catchall_24

    throw v0
.end method

.method public static removeApp(Landroid/content/Context;)V
    .registers 3

    .prologue
    .line 69
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x15

    if-lt v0, v1, :cond_30

    .line 70
    const-string v0, "activity"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 71
    if-eqz v0, :cond_30

    .line 72
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getAppTasks()Ljava/util/List;

    move-result-object v0

    .line 73
    if-eqz v0, :cond_30

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_30

    .line 74
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_20
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_30

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager$AppTask;

    .line 75
    invoke-virtual {v0}, Landroid/app/ActivityManager$AppTask;->finishAndRemoveTask()V

    goto :goto_20

    .line 80
    :cond_30
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    .line 81
    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/System;->exit(I)V

    .line 82
    return-void
.end method


# virtual methods
.method public declared-synchronized delayNotify(Ljava/lang/String;)V
    .registers 6

    .prologue
    .line 124
    monitor-enter p0

    :try_start_1
    iput-object p1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->toastContent:Ljava/lang/String;

    .line 125
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/jiagu/payegis/utils/ActivityUtil;->setQuit(Z)V

    .line 127
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->handler:Landroid/os/Handler;

    iget-object v1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->r:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 128
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->handler:Landroid/os/Handler;

    iget-object v1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->r:Ljava/lang/Runnable;

    const-wide/16 v2, 0x3e8

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z
    :try_end_17
    .catchall {:try_start_1 .. :try_end_17} :catchall_19

    .line 129
    monitor-exit p0

    return-void

    .line 124
    :catchall_19
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public isQuit()Z
    .registers 2

    .prologue
    .line 113
    iget-boolean v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->isQuit:Z

    return v0
.end method

.method public declared-synchronized removeNotify()V
    .registers 3

    .prologue
    .line 135
    monitor-enter p0

    :try_start_1
    invoke-virtual {p0}, Lcom/jiagu/payegis/utils/ActivityUtil;->isQuit()Z

    move-result v0

    if-eqz v0, :cond_12

    .line 136
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/jiagu/payegis/utils/ActivityUtil;->setQuit(Z)V

    .line 137
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->handler:Landroid/os/Handler;

    iget-object v1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->r:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V
    :try_end_12
    .catchall {:try_start_1 .. :try_end_12} :catchall_14

    .line 139
    :cond_12
    monitor-exit p0

    return-void

    .line 135
    :catchall_14
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public setQuit(Z)V
    .registers 2

    .prologue
    .line 117
    iput-boolean p1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->isQuit:Z

    .line 118
    return-void
.end method

.method public showCoveredHint()V
    .registers 4

    .prologue
    .line 145
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->mContext:Landroid/content/Context;

    iget-object v1, p0, Lcom/jiagu/payegis/utils/ActivityUtil;->toastContent:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 146
    return-void
.end method
