
[time]:2025-08-26 09:47:11-[msg]:[<PERSON><PERSON><PERSON><PERSON><PERSON>] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:11-[msg]:[<PERSON><PERSON><PERSON><PERSON><PERSON>] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:12-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:47:12-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:12-[msg]:[<PERSON>seMana<PERSON>] bind end

[time]:2025-08-26 09:47:13-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:13-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:25-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:25-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:26-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:26-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:28-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:28-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:29-[msg]:[SaseManager] loginWithToken begin userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@536f528))

[time]:2025-08-26 09:47:29-[msg]:[SaseManager] getSDKVersion ********

[time]:2025-08-26 09:47:29-[msg]:[SaseManager] loginWithToken end userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@536f528))

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] stopAnonymousTunnel begin, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] stopAnonymousTunnel end, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] getPaConfig

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] bind state changed, vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] getState vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] bind state changed, vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:30-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:31-[msg]:[SaseManager] startPaWithConnList begin

[time]:2025-08-26 09:47:31-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:31-[msg]:[SaseManager] startPaWithConnList end

[time]:2025-08-26 09:47:31-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:31-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:47:33-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:47:33-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] bind state changed, vpnState:3 gaEnable:false paEnable:true

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] getState vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] bind state changed, vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:16-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:18-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:18-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:18-[msg]:[SaseManager] loginWithToken begin userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@d68ad61))

[time]:2025-08-26 09:48:18-[msg]:[SaseManager] getSDKVersion ********

[time]:2025-08-26 09:48:18-[msg]:[SaseManager] loginWithToken end userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@d68ad61))

[time]:2025-08-26 09:48:23-[msg]:[SaseManager] bind died

[time]:2025-08-26 09:48:23-[msg]:[SaseManager] rebind begin

[time]:2025-08-26 09:48:23-[msg]:[SaseManager] rebind end

[time]:2025-08-26 09:48:24-[msg]:[SaseManager] rebind service connected com.sdp.sase.core.aidl.VPNStatus@f5eccc5

[time]:2025-08-26 09:48:24-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:32-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:32-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:32-[msg]:[SaseManager] rebind state changed: 1 false false

[time]:2025-08-26 09:48:32-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:34-[msg]:[SaseManager] rebind state changed: 2 false false

[time]:2025-08-26 09:48:34-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:34-[msg]:[SaseManager] loginWithToken begin userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@8e74248))

[time]:2025-08-26 09:48:34-[msg]:[SaseManager] getSDKVersion ********

[time]:2025-08-26 09:48:34-[msg]:[SaseManager] loginWithToken end userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@8e74248))

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] stopAnonymousTunnel begin, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] rebind state changed: 3 false false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] stopAnonymousTunnel end, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] getPaConfig

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] getState vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] rebind state changed: 4 false false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] startPaWithConnList begin

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:38-[msg]:[SaseManager] startPaWithConnList end

[time]:2025-08-26 09:48:39-[msg]:[SaseManager] rebind state changed: 1 false false

[time]:2025-08-26 09:48:39-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:48:41-[msg]:[SaseManager] rebind state changed: 2 false true

[time]:2025-08-26 09:48:41-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:25-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] bind end

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:37-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:38-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:40-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:40-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:41-[msg]:[SaseManager] loginWithToken begin userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@64252bc))

[time]:2025-08-26 09:49:41-[msg]:[SaseManager] getSDKVersion ********

[time]:2025-08-26 09:49:41-[msg]:[SaseManager] loginWithToken end userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@64252bc))

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] stopAnonymousTunnel begin, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] bind state changed, vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] stopAnonymousTunnel end, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] getPaConfig

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] getState vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] bind state changed, vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:42-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:43-[msg]:[SaseManager] startPaWithConnList begin

[time]:2025-08-26 09:49:43-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:43-[msg]:[SaseManager] startPaWithConnList end

[time]:2025-08-26 09:49:43-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:43-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:45-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:46-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:49:47-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:11-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:11-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] bind end

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:12-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:14-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:14-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:15-[msg]:[SaseManager] loginWithToken begin userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@2a09c48))

[time]:2025-08-26 09:50:15-[msg]:[SaseManager] getSDKVersion ********

[time]:2025-08-26 09:50:15-[msg]:[SaseManager] loginWithToken end userLoginInfo: UserLoginInfo(host=https://idp.sase.ctct.cn:443, corpCode=HAIT, token=, idpType=, saseSDKCorpcode=, thirdLoginInfo=ThirdLoginInfo(sourceId=idsr_13d77e05-b1f0-4f74-a803-af01b26d0e47, authId=null, otherLoginInfo=com.ffcs.vpnlibrary.utils.OtherLoginInfoBody@2a09c48))

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] stopAnonymousTunnel begin, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] bind state changed, vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] getState vpnState:3 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] stopAnonymousTunnel end, vpnState:2 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] getPaConfig

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] bind state changed, vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] startPaWithConnList begin

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] getState vpnState:4 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:16-[msg]:[SaseManager] startPaWithConnList end

[time]:2025-08-26 09:50:17-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:17-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] bind state changed, vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:19-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:21-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:21-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:26-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:32-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:32-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:33-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:33-[msg]:[SaseManager] getState vpnState:2 gaEnable:false paEnable:true

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] bind end

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:53-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:50:54-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] bind end

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:06-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:07-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] unbind

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] bind begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] bind end

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:20-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] bind service connected vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] getState vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] startAnonymousTunnel begin, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] startAnonymousTunnel end, vpnState:0 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] bind state changed, vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:21-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:50-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false

[time]:2025-08-26 09:51:50-[msg]:[SaseManager] getState vpnState:1 gaEnable:false paEnable:false