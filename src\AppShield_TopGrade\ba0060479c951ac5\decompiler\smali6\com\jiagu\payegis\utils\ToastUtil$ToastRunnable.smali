.class Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;
.super Ljava/lang/Object;
.source "ToastUtil.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/utils/ToastUtil;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ToastRunnable"
.end annotation


# instance fields
.field private duration:I

.field private final mContext:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference",
            "<",
            "Landroid/content/Context;",
            ">;"
        }
    .end annotation
.end field

.field private text:Ljava/lang/CharSequence;


# direct methods
.method public constructor <init>(Landroid/content/Context;Ljava/lang/CharSequence;I)V
    .registers 5

    .prologue
    .line 206
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 207
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->mContext:Ljava/lang/ref/WeakReference;

    .line 208
    iput-object p2, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->text:Ljava/lang/CharSequence;

    .line 209
    iput p3, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->duration:I

    .line 210
    return-void
.end method


# virtual methods
.method public run()V
    .registers 4

    .prologue
    .line 215
    new-instance v1, Landroid/widget/Toast;

    iget-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    invoke-direct {v1, v0}, Landroid/widget/Toast;-><init>(Landroid/content/Context;)V

    .line 216
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->mContext:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    .line 217
    iget-object v1, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->text:Ljava/lang/CharSequence;

    invoke-virtual {v0, v1}, Landroid/widget/Toast;->setText(Ljava/lang/CharSequence;)V

    .line 218
    iget v1, p0, Lcom/jiagu/payegis/utils/ToastUtil$ToastRunnable;->duration:I

    invoke-virtual {v0, v1}, Landroid/widget/Toast;->setDuration(I)V

    .line 219
    # invokes: Lcom/jiagu/payegis/utils/ToastUtil;->hookToast(Landroid/widget/Toast;)V
    invoke-static {v0}, Lcom/jiagu/payegis/utils/ToastUtil;->access$500(Landroid/widget/Toast;)V

    .line 220
    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 221
    return-void
.end method
