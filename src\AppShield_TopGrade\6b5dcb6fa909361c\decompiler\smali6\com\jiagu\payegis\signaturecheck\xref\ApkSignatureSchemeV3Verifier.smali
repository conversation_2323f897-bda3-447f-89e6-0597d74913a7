.class public Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;
.super Ljava/lang/Object;
.source "ApkSignatureSchemeV3Verifier.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$PlatformNotSupportedException;,
        Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;,
        Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;
    }
.end annotation


# static fields
.field private static final APK_SIGNATURE_SCHEME_V3_BLOCK_ID:I = -0xfac9740

.field private static final PROOF_OF_ROTATION_ATTR_ID:I = 0x3ba06f8c

.field public static final SF_ATTRIBUTE_ANDROID_APK_SIGNED_ID:I = 0x3


# direct methods
.method public constructor <init>()V
    .registers 1

    .prologue
    .line 73
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static eHS([B)Ljava/lang/String;
    .registers 8

    .prologue
    const/4 v1, 0x0

    .line 315
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 316
    array-length v3, p0

    move v0, v1

    :goto_8
    if-ge v0, v3, :cond_21

    aget-byte v4, p0, v0

    .line 317
    const-string v5, "%02d, "

    const/4 v6, 0x1

    new-array v6, v6, [Ljava/lang/Object;

    invoke-static {v4}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v4

    aput-object v4, v6, v1

    invoke-static {v5, v6}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 316
    add-int/lit8 v0, v0, 0x1

    goto :goto_8

    .line 319
    :cond_21
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private static findSignature(Ljava/io/RandomAccessFile;)Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Lcom/jiagu/payegis/signaturecheck/xref/SignatureNotFoundException;
        }
    .end annotation

    .prologue
    .line 128
    const v0, -0xfac9740

    invoke-static {p0, v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->findSignature(Ljava/io/RandomAccessFile;I)Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;

    move-result-object v0

    return-object v0
.end method

.method private static isSupportedSignatureAlgorithm(I)Z
    .registers 2

    .prologue
    .line 456
    sparse-switch p0, :sswitch_data_8

    .line 469
    const/4 v0, 0x0

    :goto_4
    return v0

    .line 467
    :sswitch_5
    const/4 v0, 0x1

    goto :goto_4

    .line 456
    nop

    :sswitch_data_8
    .sparse-switch
        0x101 -> :sswitch_5
        0x102 -> :sswitch_5
        0x103 -> :sswitch_5
        0x104 -> :sswitch_5
        0x201 -> :sswitch_5
        0x202 -> :sswitch_5
        0x301 -> :sswitch_5
        0x421 -> :sswitch_5
        0x423 -> :sswitch_5
        0x425 -> :sswitch_5
    .end sparse-switch
.end method

.method private static verify(Ljava/io/RandomAccessFile;Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;Z)Ljava/lang/String;
    .registers 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 142
    const/4 v1, 0x0

    .line 143
    new-instance v0, Landroid/util/ArrayMap;

    invoke-direct {v0}, Landroid/util/ArrayMap;-><init>()V

    .line 147
    :try_start_6
    const-string v2, "X.509"

    invoke-static {v2}, Ljava/security/cert/CertificateFactory;->getInstance(Ljava/lang/String;)Ljava/security/cert/CertificateFactory;
    :try_end_b
    .catch Ljava/security/cert/CertificateException; {:try_start_6 .. :try_end_b} :catch_21

    move-result-object v2

    .line 153
    :try_start_c
    iget-object v3, p1, Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;->signatureBlock:Ljava/nio/ByteBuffer;

    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;
    :try_end_11
    .catch Ljava/io/IOException; {:try_start_c .. :try_end_11} :catch_2a

    move-result-object v3

    .line 157
    :goto_12
    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v4

    if-eqz v4, :cond_53

    .line 159
    :try_start_18
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v4

    .line 160
    invoke-static {v4, v0, v2}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verifySigner(Ljava/nio/ByteBuffer;Ljava/util/Map;Ljava/security/cert/CertificateFactory;)Ljava/lang/String;
    :try_end_1f
    .catch Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$PlatformNotSupportedException; {:try_start_18 .. :try_end_1f} :catch_5a
    .catch Ljava/io/IOException; {:try_start_18 .. :try_end_1f} :catch_33
    .catch Ljava/nio/BufferUnderflowException; {:try_start_18 .. :try_end_1f} :catch_56
    .catch Ljava/lang/SecurityException; {:try_start_18 .. :try_end_1f} :catch_58

    move-result-object v0

    .line 170
    :goto_20
    return-object v0

    .line 148
    :catch_21
    move-exception v0

    .line 149
    new-instance v1, Ljava/lang/RuntimeException;

    const-string v2, "Failed to obtain X.509 CertificateFactory"

    invoke-direct {v1, v2, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 154
    :catch_2a
    move-exception v0

    .line 155
    new-instance v1, Ljava/lang/SecurityException;

    const-string v2, "Failed to read list of signers"

    invoke-direct {v1, v2, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 164
    :catch_33
    move-exception v0

    .line 165
    :goto_34
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to parse/verify signer #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " block"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v2

    .line 170
    :cond_53
    const-string v0, "no V3"

    goto :goto_20

    .line 164
    :catch_56
    move-exception v0

    goto :goto_34

    :catch_58
    move-exception v0

    goto :goto_34

    .line 161
    :catch_5a
    move-exception v4

    goto :goto_12
.end method

.method private static verify(Ljava/io/RandomAccessFile;Z)Ljava/lang/String;
    .registers 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/jiagu/payegis/signaturecheck/xref/SignatureNotFoundException;,
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 115
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->findSignature(Ljava/io/RandomAccessFile;)Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;

    move-result-object v0

    .line 116
    invoke-static {p0, v0, p1}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verify(Ljava/io/RandomAccessFile;Lcom/jiagu/payegis/signaturecheck/xref/SignatureInfo;Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public static verify(Ljava/lang/String;)Ljava/lang/String;
    .registers 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/jiagu/payegis/signaturecheck/xref/SignatureNotFoundException;,
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 94
    const/4 v0, 0x0

    invoke-static {p0, v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verify(Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private static verify(Ljava/lang/String;Z)Ljava/lang/String;
    .registers 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/jiagu/payegis/signaturecheck/xref/SignatureNotFoundException;,
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 99
    new-instance v2, Ljava/io/RandomAccessFile;

    const-string v0, "r"

    invoke-direct {v2, p0, v0}, Ljava/io/RandomAccessFile;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x0

    .line 100
    :try_start_8
    invoke-static {v2, p1}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verify(Ljava/io/RandomAccessFile;Z)Ljava/lang/String;
    :try_end_b
    .catch Ljava/lang/Throwable; {:try_start_8 .. :try_end_b} :catch_1d
    .catchall {:try_start_8 .. :try_end_b} :catchall_1f

    move-result-object v0

    .line 101
    if-eqz v2, :cond_13

    if-eqz v1, :cond_19

    :try_start_10
    invoke-virtual {v2}, Ljava/io/RandomAccessFile;->close()V
    :try_end_13
    .catch Ljava/lang/Throwable; {:try_start_10 .. :try_end_13} :catch_14

    .line 100
    :cond_13
    :goto_13
    return-object v0

    .line 101
    :catch_14
    move-exception v2

    invoke-virtual {v1, v2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    goto :goto_13

    :cond_19
    invoke-virtual {v2}, Ljava/io/RandomAccessFile;->close()V

    goto :goto_13

    .line 99
    :catch_1d
    move-exception v1

    :try_start_1e
    throw v1
    :try_end_1f
    .catchall {:try_start_1e .. :try_end_1f} :catchall_1f

    .line 101
    :catchall_1f
    move-exception v0

    if-eqz v2, :cond_27

    if-eqz v1, :cond_2d

    :try_start_24
    invoke-virtual {v2}, Ljava/io/RandomAccessFile;->close()V
    :try_end_27
    .catch Ljava/lang/Throwable; {:try_start_24 .. :try_end_27} :catch_28

    :cond_27
    :goto_27
    throw v0

    :catch_28
    move-exception v2

    invoke-virtual {v1, v2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    goto :goto_27

    :cond_2d
    invoke-virtual {v2}, Ljava/io/RandomAccessFile;->close()V

    goto :goto_27
.end method

.method private static verifyAdditionalAttributes(Ljava/nio/ByteBuffer;Ljava/util/List;Ljava/security/cert/CertificateFactory;)Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;
    .registers 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/nio/ByteBuffer;",
            "Ljava/util/List",
            "<",
            "Ljava/security/cert/X509Certificate;",
            ">;",
            "Ljava/security/cert/CertificateFactory;",
            ")",
            "Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 325
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    new-array v0, v0, [Ljava/security/cert/X509Certificate;

    invoke-interface {p1, v0}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/security/cert/X509Certificate;

    .line 326
    const/4 v1, 0x0

    .line 328
    :goto_d
    invoke-virtual {p0}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v2

    if-eqz v2, :cond_8d

    .line 329
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v2

    .line 330
    invoke-virtual {v2}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v3

    const/4 v4, 0x4

    if-ge v3, v4, :cond_3b

    .line 331
    new-instance v0, Ljava/io/IOException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Remaining buffer too short to contain additional attribute ID. Remaining: "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 332
    invoke-virtual {v2}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 334
    :cond_3b
    invoke-virtual {v2}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v3

    .line 335
    packed-switch v3, :pswitch_data_94

    goto :goto_d

    .line 337
    :pswitch_43
    if-eqz v1, :cond_4d

    .line 338
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "Encountered multiple Proof-of-rotation records when verifying APK Signature Scheme v3 signature"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 341
    :cond_4d
    invoke-static {v2, p2}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verifyProofOfRotationStruct(Ljava/nio/ByteBuffer;Ljava/security/cert/CertificateFactory;)Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;

    move-result-object v2

    .line 345
    :try_start_51
    iget-object v1, v2, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;->certs:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_8b

    iget-object v1, v2, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;->certs:Ljava/util/List;

    iget-object v3, v2, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;->certs:Ljava/util/List;

    .line 346
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    add-int/lit8 v3, v3, -0x1

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/security/cert/X509Certificate;

    invoke-virtual {v1}, Ljava/security/cert/X509Certificate;->getEncoded()[B

    move-result-object v1

    const/4 v3, 0x0

    aget-object v3, v0, v3

    .line 347
    invoke-virtual {v3}, Ljava/security/cert/X509Certificate;->getEncoded()[B

    move-result-object v3

    .line 346
    invoke-static {v1, v3}, Ljava/util/Arrays;->equals([B[B)Z

    move-result v1

    if-nez v1, :cond_8b

    .line 348
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "Terminal certificate in Proof-of-rotation record does not match APK signing certificate"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_82
    .catch Ljava/security/cert/CertificateEncodingException; {:try_start_51 .. :try_end_82} :catch_82

    .line 351
    :catch_82
    move-exception v0

    .line 352
    new-instance v1, Ljava/lang/SecurityException;

    const-string v2, "Failed to encode certificate when comparing Proof-of-rotation record and signing certificate"

    invoke-direct {v1, v2, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    :cond_8b
    move-object v1, v2

    .line 354
    goto :goto_d

    .line 362
    :cond_8d
    new-instance v2, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;

    invoke-direct {v2, v0, v1}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedSigner;-><init>([Ljava/security/cert/X509Certificate;Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;)V

    return-object v2

    .line 335
    nop

    :pswitch_data_94
    .packed-switch 0x3ba06f8c
        :pswitch_43
    .end packed-switch
.end method

.method private static verifyProofOfRotationStruct(Ljava/nio/ByteBuffer;Ljava/security/cert/CertificateFactory;)Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;
    .registers 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 369
    const/4 v1, 0x0

    .line 370
    const/4 v3, -0x1

    .line 371
    const/4 v0, 0x0

    .line 372
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 373
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 392
    :try_start_d
    invoke-virtual {p0}, Ljava/nio/ByteBuffer;->getInt()I

    .line 393
    new-instance v7, Ljava/util/HashSet;

    invoke-direct {v7}, Ljava/util/HashSet;-><init>()V

    move-object v2, v0

    .line 394
    :goto_16
    invoke-virtual {p0}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_146

    .line 395
    add-int/lit8 v1, v1, 0x1

    .line 396
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v0

    .line 397
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v8

    .line 398
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v9

    .line 399
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v4

    .line 400
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B

    move-result-object v10

    .line 402
    if-eqz v2, :cond_91

    .line 405
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getSignatureAlgorithmJcaSignatureAlgorithm(I)Lcom/jiagu/payegis/signaturecheck/xref/Pair;

    move-result-object v11

    .line 406
    invoke-virtual {v2}, Ljava/security/cert/X509Certificate;->getPublicKey()Ljava/security/PublicKey;

    move-result-object v12

    .line 407
    iget-object v0, v11, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    invoke-static {v0}, Ljava/security/Signature;->getInstance(Ljava/lang/String;)Ljava/security/Signature;

    move-result-object v13

    .line 408
    invoke-virtual {v13, v12}, Ljava/security/Signature;->initVerify(Ljava/security/PublicKey;)V

    .line 409
    iget-object v0, v11, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->second:Ljava/lang/Object;

    if-eqz v0, :cond_52

    .line 410
    iget-object v0, v11, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/security/spec/AlgorithmParameterSpec;

    invoke-virtual {v13, v0}, Ljava/security/Signature;->setParameter(Ljava/security/spec/AlgorithmParameterSpec;)V

    .line 412
    :cond_52
    invoke-virtual {v13, v8}, Ljava/security/Signature;->update(Ljava/nio/ByteBuffer;)V

    .line 413
    invoke-virtual {v13, v10}, Ljava/security/Signature;->verify([B)Z

    move-result v0

    if-nez v0, :cond_91

    .line 414
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unable to verify signature of certificate #"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " using "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v0, v11, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " when verifying Proof-of-rotation record"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v2, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v2
    :try_end_88
    .catch Ljava/io/IOException; {:try_start_d .. :try_end_88} :catch_88
    .catch Ljava/nio/BufferUnderflowException; {:try_start_d .. :try_end_88} :catch_bf
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_d .. :try_end_88} :catch_f6
    .catch Ljava/security/InvalidKeyException; {:try_start_d .. :try_end_88} :catch_14e
    .catch Ljava/security/InvalidAlgorithmParameterException; {:try_start_d .. :try_end_88} :catch_14c
    .catch Ljava/security/SignatureException; {:try_start_d .. :try_end_88} :catch_150
    .catch Ljava/security/cert/CertificateException; {:try_start_d .. :try_end_88} :catch_126

    .line 441
    :catch_88
    move-exception v0

    .line 442
    :goto_89
    new-instance v1, Ljava/io/IOException;

    const-string v2, "Failed to parse Proof-of-rotation record"

    invoke-direct {v1, v2, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 420
    :cond_91
    :try_start_91
    invoke-virtual {v8}, Ljava/nio/ByteBuffer;->rewind()Ljava/nio/Buffer;

    .line 421
    invoke-static {v8}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B

    move-result-object v10

    .line 422
    invoke-virtual {v8}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v0

    .line 423
    if-eqz v2, :cond_c1

    if-eq v3, v0, :cond_c1

    .line 424
    new-instance v0, Ljava/lang/SecurityException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Signing algorithm ID mismatch for certificate #"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " when verifying Proof-of-rotation record"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v2}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 441
    :catch_bf
    move-exception v0

    goto :goto_89

    .line 427
    :cond_c1
    new-instance v0, Ljava/io/ByteArrayInputStream;

    invoke-direct {v0, v10}, Ljava/io/ByteArrayInputStream;-><init>([B)V

    .line 428
    invoke-virtual {p1, v0}, Ljava/security/cert/CertificateFactory;->generateCertificate(Ljava/io/InputStream;)Ljava/security/cert/Certificate;

    move-result-object v0

    check-cast v0, Ljava/security/cert/X509Certificate;

    .line 429
    new-instance v2, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;

    invoke-direct {v2, v0, v10}, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;-><init>(Ljava/security/cert/X509Certificate;[B)V

    .line 432
    invoke-virtual {v7, v2}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_116

    .line 433
    new-instance v0, Ljava/lang/SecurityException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Encountered duplicate entries in Proof-of-rotation record at certificate #"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ".  All signing certificates should be unique"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v2}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_f6
    .catch Ljava/io/IOException; {:try_start_91 .. :try_end_f6} :catch_88
    .catch Ljava/nio/BufferUnderflowException; {:try_start_91 .. :try_end_f6} :catch_bf
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_91 .. :try_end_f6} :catch_f6
    .catch Ljava/security/InvalidKeyException; {:try_start_91 .. :try_end_f6} :catch_14e
    .catch Ljava/security/InvalidAlgorithmParameterException; {:try_start_91 .. :try_end_f6} :catch_14c
    .catch Ljava/security/SignatureException; {:try_start_91 .. :try_end_f6} :catch_150
    .catch Ljava/security/cert/CertificateException; {:try_start_91 .. :try_end_f6} :catch_126

    .line 443
    :catch_f6
    move-exception v0

    .line 445
    :goto_f7
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to verify signature over signed data for certificate #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " when verifying Proof-of-rotation record"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v2

    .line 437
    :cond_116
    :try_start_116
    invoke-virtual {v7, v2}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 438
    invoke-interface {v5, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 439
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_123
    .catch Ljava/io/IOException; {:try_start_116 .. :try_end_123} :catch_88
    .catch Ljava/nio/BufferUnderflowException; {:try_start_116 .. :try_end_123} :catch_bf
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_116 .. :try_end_123} :catch_f6
    .catch Ljava/security/InvalidKeyException; {:try_start_116 .. :try_end_123} :catch_14e
    .catch Ljava/security/InvalidAlgorithmParameterException; {:try_start_116 .. :try_end_123} :catch_14c
    .catch Ljava/security/SignatureException; {:try_start_116 .. :try_end_123} :catch_150
    .catch Ljava/security/cert/CertificateException; {:try_start_116 .. :try_end_123} :catch_126

    move v3, v4

    .line 440
    goto/16 :goto_16

    .line 448
    :catch_126
    move-exception v0

    .line 449
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to decode certificate #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " when verifying Proof-of-rotation record"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v2

    .line 452
    :cond_146
    new-instance v0, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;

    invoke-direct {v0, v5, v6}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$VerifiedProofOfRotation;-><init>(Ljava/util/List;Ljava/util/List;)V

    return-object v0

    .line 443
    :catch_14c
    move-exception v0

    goto :goto_f7

    :catch_14e
    move-exception v0

    goto :goto_f7

    :catch_150
    move-exception v0

    goto :goto_f7
.end method

.method private static verifySigner(Ljava/nio/ByteBuffer;Ljava/util/Map;Ljava/security/cert/CertificateFactory;)Ljava/lang/String;
    .registers 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/nio/ByteBuffer;",
            "Ljava/util/Map",
            "<",
            "Ljava/lang/Integer;",
            "[B>;",
            "Ljava/security/cert/CertificateFactory;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/SecurityException;,
            Ljava/io/IOException;,
            Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier$PlatformNotSupportedException;
        }
    .end annotation

    .prologue
    .line 178
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v5

    .line 179
    invoke-virtual {p0}, Ljava/nio/ByteBuffer;->getInt()I

    .line 180
    invoke-virtual {p0}, Ljava/nio/ByteBuffer;->getInt()I

    .line 182
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v6

    .line 183
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B

    move-result-object v7

    .line 185
    const/4 v0, 0x0

    .line 186
    const/4 v3, -0x1

    .line 187
    const/4 v2, 0x0

    .line 188
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    move v4, v0

    .line 189
    :cond_1b
    :goto_1b
    invoke-virtual {v6}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_72

    .line 190
    add-int/lit8 v4, v4, 0x1

    .line 192
    :try_start_23
    invoke-static {v6}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v0

    .line 193
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v1

    const/16 v9, 0x8

    if-ge v1, v9, :cond_51

    .line 194
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "Signature record too short"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_37
    .catch Ljava/io/IOException; {:try_start_23 .. :try_end_37} :catch_37
    .catch Ljava/nio/BufferUnderflowException; {:try_start_23 .. :try_end_37} :catch_20e

    .line 206
    :catch_37
    move-exception v0

    .line 207
    :goto_38
    new-instance v1, Ljava/lang/SecurityException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Failed to parse signature record #"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 196
    :cond_51
    :try_start_51
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v1

    .line 197
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 198
    invoke-static {v1}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->isSupportedSignatureAlgorithm(I)Z

    move-result v9

    if-eqz v9, :cond_1b

    .line 201
    const/4 v9, -0x1

    if-eq v3, v9, :cond_6b

    .line 202
    invoke-static {v1, v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->compareSignatureAlgorithm(II)I

    move-result v9

    if-lez v9, :cond_214

    .line 204
    :cond_6b
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B
    :try_end_6e
    .catch Ljava/io/IOException; {:try_start_51 .. :try_end_6e} :catch_37
    .catch Ljava/nio/BufferUnderflowException; {:try_start_51 .. :try_end_6e} :catch_20e

    move-result-object v0

    :goto_6f
    move-object v2, v0

    move v3, v1

    .line 210
    goto :goto_1b

    .line 212
    :cond_72
    const/4 v0, -0x1

    if-ne v3, v0, :cond_87

    .line 213
    if-nez v4, :cond_7f

    .line 214
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "No signatures found"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 216
    :cond_7f
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "No supported signatures found"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 220
    :cond_87
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getSignatureAlgorithmJcaKeyAlgorithm(I)Ljava/lang/String;

    move-result-object v4

    .line 222
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getSignatureAlgorithmJcaSignatureAlgorithm(I)Lcom/jiagu/payegis/signaturecheck/xref/Pair;

    move-result-object v1

    .line 223
    iget-object v0, v1, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    .line 224
    iget-object v1, v1, Lcom/jiagu/payegis/signaturecheck/xref/Pair;->second:Ljava/lang/Object;

    check-cast v1, Ljava/security/spec/AlgorithmParameterSpec;

    .line 228
    :try_start_97
    invoke-static {v4}, Ljava/security/KeyFactory;->getInstance(Ljava/lang/String;)Ljava/security/KeyFactory;

    move-result-object v4

    new-instance v6, Ljava/security/spec/X509EncodedKeySpec;

    invoke-direct {v6, v7}, Ljava/security/spec/X509EncodedKeySpec;-><init>([B)V

    .line 229
    invoke-virtual {v4, v6}, Ljava/security/KeyFactory;->generatePublic(Ljava/security/spec/KeySpec;)Ljava/security/PublicKey;

    move-result-object v4

    .line 230
    invoke-static {v0}, Ljava/security/Signature;->getInstance(Ljava/lang/String;)Ljava/security/Signature;

    move-result-object v6

    .line 231
    invoke-virtual {v6, v4}, Ljava/security/Signature;->initVerify(Ljava/security/PublicKey;)V

    .line 232
    if-eqz v1, :cond_b0

    .line 233
    invoke-virtual {v6, v1}, Ljava/security/Signature;->setParameter(Ljava/security/spec/AlgorithmParameterSpec;)V

    .line 235
    :cond_b0
    invoke-virtual {v6, v5}, Ljava/security/Signature;->update(Ljava/nio/ByteBuffer;)V

    .line 236
    invoke-virtual {v6, v2}, Ljava/security/Signature;->verify([B)Z
    :try_end_b6
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_97 .. :try_end_b6} :catch_202
    .catch Ljava/security/spec/InvalidKeySpecException; {:try_start_97 .. :try_end_b6} :catch_20b
    .catch Ljava/security/InvalidKeyException; {:try_start_97 .. :try_end_b6} :catch_205
    .catch Ljava/security/InvalidAlgorithmParameterException; {:try_start_97 .. :try_end_b6} :catch_d2
    .catch Ljava/security/SignatureException; {:try_start_97 .. :try_end_b6} :catch_208

    move-result v1

    .line 242
    if-nez v1, :cond_f2

    .line 243
    new-instance v1, Ljava/lang/SecurityException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " signature did not verify"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v1

    .line 237
    :catch_d2
    move-exception v1

    .line 239
    :goto_d3
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to verify "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " signature"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v2, v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v2

    .line 248
    :cond_f2
    const/4 v1, 0x0

    .line 249
    invoke-virtual {v5}, Ljava/nio/ByteBuffer;->clear()Ljava/nio/Buffer;

    .line 250
    invoke-static {v5}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v4

    .line 251
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 252
    const/4 v0, 0x0

    move v2, v0

    .line 253
    :goto_101
    invoke-virtual {v4}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_14a

    .line 254
    add-int/lit8 v2, v2, 0x1

    .line 256
    :try_start_109
    invoke-static {v4}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v0

    .line 257
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v7

    const/16 v9, 0x8

    if-ge v7, v9, :cond_137

    .line 258
    new-instance v0, Ljava/io/IOException;

    const-string v1, "Record too short"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_11d
    .catch Ljava/io/IOException; {:try_start_109 .. :try_end_11d} :catch_11d
    .catch Ljava/nio/BufferUnderflowException; {:try_start_109 .. :try_end_11d} :catch_1ff

    .line 265
    :catch_11d
    move-exception v0

    .line 266
    :goto_11e
    new-instance v1, Ljava/io/IOException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to parse digest record #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 260
    :cond_137
    :try_start_137
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->getInt()I

    move-result v7

    .line 261
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-interface {v6, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 262
    if-ne v7, v3, :cond_211

    .line 263
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B
    :try_end_147
    .catch Ljava/io/IOException; {:try_start_137 .. :try_end_147} :catch_11d
    .catch Ljava/nio/BufferUnderflowException; {:try_start_137 .. :try_end_147} :catch_1ff

    move-result-object v0

    :goto_148
    move-object v1, v0

    .line 267
    goto :goto_101

    .line 270
    :cond_14a
    invoke-interface {v8, v6}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_158

    .line 271
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "Signature algorithms don\'t match between digests and signatures records"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 274
    :cond_158
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getSignatureAlgorithmContentDigestAlgorithm(I)I

    move-result v2

    .line 275
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [B

    .line 276
    if-eqz v0, :cond_18b

    .line 277
    invoke-static {v0, v1}, Ljava/security/MessageDigest;->isEqual([B[B)Z

    move-result v0

    if-nez v0, :cond_18b

    .line 278
    new-instance v0, Ljava/lang/SecurityException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 279
    invoke-static {v2}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getContentDigestAlgorithmJcaDigestAlgorithm(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " contents digest does not match the digest specified by a preceding signer"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 283
    :cond_18b
    invoke-static {v5}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->getLengthPrefixedSlice(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    move-result-object v2

    .line 284
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 285
    const/4 v0, 0x0

    .line 286
    :goto_195
    invoke-virtual {v2}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v1

    if-eqz v1, :cond_1d0

    .line 287
    add-int/lit8 v1, v0, 0x1

    .line 288
    invoke-static {v2}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSigningBlockUtils;->readLengthPrefixedByteArray(Ljava/nio/ByteBuffer;)[B

    move-result-object v4

    .line 291
    :try_start_1a1
    new-instance v0, Ljava/io/ByteArrayInputStream;

    invoke-direct {v0, v4}, Ljava/io/ByteArrayInputStream;-><init>([B)V

    .line 292
    invoke-virtual {p2, v0}, Ljava/security/cert/CertificateFactory;->generateCertificate(Ljava/io/InputStream;)Ljava/security/cert/Certificate;

    move-result-object v0

    check-cast v0, Ljava/security/cert/X509Certificate;
    :try_end_1ac
    .catch Ljava/security/cert/CertificateException; {:try_start_1a1 .. :try_end_1ac} :catch_1b6

    .line 296
    new-instance v5, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;

    invoke-direct {v5, v0, v4}, Lcom/jiagu/payegis/signaturecheck/xref/VerbatimX509Certificate;-><init>(Ljava/security/cert/X509Certificate;[B)V

    .line 298
    invoke-interface {v3, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move v0, v1

    .line 299
    goto :goto_195

    .line 293
    :catch_1b6
    move-exception v0

    .line 294
    new-instance v2, Ljava/lang/SecurityException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Failed to decode certificate #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1, v0}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v2

    .line 301
    :cond_1d0
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1de

    .line 302
    new-instance v0, Ljava/lang/SecurityException;

    const-string v1, "No certificates listed"

    invoke-direct {v0, v1}, Ljava/lang/SecurityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 304
    :cond_1de
    const/4 v0, 0x0

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/security/cert/X509Certificate;

    .line 305
    invoke-virtual {v0}, Ljava/security/cert/X509Certificate;->getPublicKey()Ljava/security/PublicKey;

    move-result-object v0

    invoke-interface {v0}, Ljava/security/PublicKey;->getEncoded()[B

    move-result-object v0

    .line 308
    invoke-static {v0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->eHS([B)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    .line 309
    if-nez v0, :cond_1fa

    .line 310
    const-string v0, "chs == 0"

    .line 312
    :goto_1f9
    return-object v0

    :cond_1fa
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    goto :goto_1f9

    .line 265
    :catch_1ff
    move-exception v0

    goto/16 :goto_11e

    .line 237
    :catch_202
    move-exception v1

    goto/16 :goto_d3

    :catch_205
    move-exception v1

    goto/16 :goto_d3

    :catch_208
    move-exception v1

    goto/16 :goto_d3

    :catch_20b
    move-exception v1

    goto/16 :goto_d3

    .line 206
    :catch_20e
    move-exception v0

    goto/16 :goto_38

    :cond_211
    move-object v0, v1

    goto/16 :goto_148

    :cond_214
    move-object v0, v2

    move v1, v3

    goto/16 :goto_6f
.end method
