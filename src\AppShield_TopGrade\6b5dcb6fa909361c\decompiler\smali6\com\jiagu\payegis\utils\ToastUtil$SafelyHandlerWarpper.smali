.class Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;
.super Landroid/os/Handler;
.source "ToastUtil.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/jiagu/payegis/utils/ToastUtil;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "SafelyHandlerWarpper"
.end annotation


# instance fields
.field private originHandler:Landroid/os/Handler;


# direct methods
.method public constructor <init>(Landroid/os/Handler;)V
    .registers 2

    .prologue
    .line 230
    invoke-direct {p0}, Landroid/os/Handler;-><init>()V

    .line 231
    iput-object p1, p0, Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;->originHandler:Landroid/os/Handler;

    .line 232
    return-void
.end method


# virtual methods
.method public dispatchMessage(Landroid/os/Message;)V
    .registers 6

    .prologue
    .line 240
    :try_start_0
    invoke-super {p0, p1}, Landroid/os/Handler;->dispatchMessage(Landroid/os/Message;)V
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_3} :catch_4

    .line 244
    :goto_3
    return-void

    .line 241
    :catch_4
    move-exception v0

    .line 242
    const-string v1, "ToastUtil"

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Catch system toast exception:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_3
.end method

.method public handleMessage(Landroid/os/Message;)V
    .registers 3

    .prologue
    .line 249
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;->originHandler:Landroid/os/Handler;

    if-eqz v0, :cond_9

    .line 250
    iget-object v0, p0, Lcom/jiagu/payegis/utils/ToastUtil$SafelyHandlerWarpper;->originHandler:Landroid/os/Handler;

    invoke-virtual {v0, p1}, Landroid/os/Handler;->handleMessage(Landroid/os/Message;)V

    .line 252
    :cond_9
    return-void
.end method
