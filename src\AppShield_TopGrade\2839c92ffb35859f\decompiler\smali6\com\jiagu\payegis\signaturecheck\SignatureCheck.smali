.class public Lcom/jiagu/payegis/signaturecheck/SignatureCheck;
.super Ljava/lang/Object;
.source "SignatureCheck.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;
    }
.end annotation


# static fields
.field private static final FILE_NAME:Ljava/lang/String; = "PK"

.field public static ISBLOCK:Ljava/lang/Boolean; = null

.field private static final MSG:Ljava/lang/String; = "The App Sinature Is Error!"

.field private static final MSG_CN:Ljava/lang/String; = "\u5b89\u5168\u63d0\u793a\uff1aAPP\u52a0\u56fa\u524d\u540e\u7b7e\u540d\u4fe1\u606f\u4e0d\u4e00\u81f4\uff0c\u8bf7\u91cd\u65b0\u786e\u8ba4\u7b7e\u540d\u4fe1\u606f\u3002"

.field private static final TAG:Ljava/lang/String; = "Payegis"

.field private static final algorithm:Ljava/lang/String; = "SHA256"


# direct methods
.method static constructor <clinit>()V
    .registers 1

    .prologue
    .line 43
    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->ISBLOCK:Ljava/lang/Boolean;

    return-void
.end method

.method public constructor <init>()V
    .registers 1

    .prologue
    .line 36
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000(Ljava/lang/String;)Ljava/lang/String;
    .registers 2

    .prologue
    .line 36
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->getApkSignature(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method static synthetic access$100(Landroid/content/Context;)Ljava/lang/String;
    .registers 2

    .prologue
    .line 36
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->getReleaseSignature(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method static synthetic access$200(Landroid/content/Context;)Ljava/lang/Boolean;
    .registers 2

    .prologue
    .line 36
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->checkCreator(Landroid/content/Context;)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method

.method private static checkCreator(Landroid/content/Context;)Ljava/lang/Boolean;
    .registers 3

    .prologue
    .line 109
    const-string v0, "android.content.pm.PackageInfo$1"

    .line 110
    const-string v1, ""

    .line 112
    :try_start_4
    sget-object v1, Landroid/content/pm/PackageInfo;->CREATOR:Landroid/os/Parcelable$Creator;

    .line 113
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    .line 115
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1e

    .line 116
    const/4 v0, 0x1

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;
    :try_end_18
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_18} :catch_1a

    move-result-object v0

    .line 122
    :goto_19
    return-object v0

    .line 119
    :catch_1a
    move-exception v0

    .line 120
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 122
    :cond_1e
    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    goto :goto_19
.end method

.method public static checkSignature(Landroid/content/Context;Ljava/lang/String;)V
    .registers 6

    .prologue
    .line 86
    new-instance v0, Landroid/os/Handler;

    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    .line 87
    new-instance v1, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;

    invoke-direct {v1, p0, p1}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck$DoneRunnable;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    const-wide/16 v2, 0x32

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 88
    return-void
.end method

.method public static checkSignatureBlock(Landroid/content/Context;Ljava/lang/String;)V
    .registers 3

    .prologue
    .line 91
    const/4 v0, 0x1

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    sput-object v0, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->ISBLOCK:Ljava/lang/Boolean;

    .line 92
    invoke-static {p0, p1}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->checkSignature(Landroid/content/Context;Ljava/lang/String;)V

    .line 93
    return-void
.end method

.method private static decodeSignatureInfo([B)V
    .registers 3

    .prologue
    .line 196
    const/4 v0, 0x0

    :goto_1
    array-length v1, p0

    if-ge v0, v1, :cond_1c

    .line 197
    aget-byte v1, p0, v0

    xor-int/lit8 v1, v1, 0x65

    int-to-byte v1, v1

    aput-byte v1, p0, v0

    .line 198
    aget-byte v1, p0, v0

    xor-int/lit8 v1, v1, 0x56

    int-to-byte v1, v1

    aput-byte v1, p0, v0

    .line 199
    aget-byte v1, p0, v0

    add-int/lit8 v1, v1, -0x1

    int-to-byte v1, v1

    aput-byte v1, p0, v0

    .line 196
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 201
    :cond_1c
    return-void
.end method

.method public static getApkSignInfo(Ljava/lang/String;)I
    .registers 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/security/NoSuchAlgorithmException;,
            Ljava/security/cert/CertificateEncodingException;,
            Ljava/lang/NoSuchFieldException;,
            Ljava/lang/IllegalAccessException;,
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .prologue
    const/4 v1, 0x0

    .line 128
    new-instance v2, Ljava/util/jar/JarFile;

    invoke-direct {v2, p0}, Ljava/util/jar/JarFile;-><init>(Ljava/lang/String;)V

    .line 129
    invoke-virtual {v2}, Ljava/util/jar/JarFile;->entries()Ljava/util/Enumeration;

    .line 130
    const/16 v0, 0x2000

    new-array v3, v0, [B

    .line 133
    const-string v0, "AndroidManifest.xml"

    invoke-virtual {v2, v0}, Ljava/util/jar/JarFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v0

    check-cast v0, Ljava/util/jar/JarEntry;

    .line 134
    invoke-static {v2, v0, v3}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->loadCertificates(Ljava/util/jar/JarFile;Ljava/util/jar/JarEntry;[B)[Ljava/security/cert/Certificate;

    move-result-object v0

    .line 135
    invoke-virtual {v2}, Ljava/util/jar/JarFile;->close()V

    .line 137
    if-nez v0, :cond_20

    move v0, v1

    .line 141
    :goto_1f
    return v0

    :cond_20
    aget-object v0, v0, v1

    invoke-virtual {v0}, Ljava/security/cert/Certificate;->getEncoded()[B

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->hashCode([B)I

    move-result v0

    goto :goto_1f
.end method

.method private static getApkSignature(Ljava/lang/String;)Ljava/lang/String;
    .registers 3

    .prologue
    const/4 v0, 0x0

    .line 146
    :try_start_1
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV3Verifier;->verify(Ljava/lang/String;)Ljava/lang/String;
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_4} :catch_6

    move-result-object v0

    .line 164
    :goto_5
    return-object v0

    .line 147
    :catch_6
    move-exception v1

    .line 150
    :try_start_7
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/xref/ApkSignatureSchemeV2Verifier;->verify(Ljava/lang/String;)Ljava/lang/String;
    :try_end_a
    .catch Ljava/lang/Exception; {:try_start_7 .. :try_end_a} :catch_18

    move-result-object v1

    .line 151
    if-nez v1, :cond_16

    .line 153
    :try_start_d
    invoke-static {p0}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->getApkSignInfo(Ljava/lang/String;)I

    move-result v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;
    :try_end_14
    .catch Ljava/lang/Exception; {:try_start_d .. :try_end_14} :catch_1a

    move-result-object v0

    goto :goto_5

    :cond_16
    move-object v0, v1

    .line 159
    goto :goto_5

    .line 161
    :catch_18
    move-exception v1

    goto :goto_5

    .line 154
    :catch_1a
    move-exception v1

    goto :goto_5
.end method

.method private static getReleaseSignature(Landroid/content/Context;)Ljava/lang/String;
    .registers 5

    .prologue
    const/4 v0, 0x0

    .line 168
    .line 171
    :try_start_1
    invoke-virtual {p0}, Landroid/content/Context;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v1

    .line 172
    const-string v2, "PK"

    invoke-virtual {v1, v2}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;
    :try_end_a
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_a} :catch_25
    .catchall {:try_start_1 .. :try_end_a} :catchall_2f

    move-result-object v1

    .line 173
    :try_start_b
    invoke-virtual {v1}, Ljava/io/InputStream;->available()I

    move-result v2

    .line 175
    if-lez v2, :cond_1f

    .line 176
    new-array v3, v2, [B

    .line 177
    invoke-virtual {v1, v3}, Ljava/io/InputStream;->read([B)I

    .line 178
    invoke-static {v3}, Lcom/jiagu/payegis/signaturecheck/SignatureCheck;->decodeSignatureInfo([B)V

    .line 179
    new-instance v2, Ljava/lang/String;

    invoke-direct {v2, v3}, Ljava/lang/String;-><init>([B)V
    :try_end_1e
    .catch Ljava/io/IOException; {:try_start_b .. :try_end_1e} :catch_40
    .catchall {:try_start_b .. :try_end_1e} :catchall_3c

    move-object v0, v2

    .line 184
    :cond_1f
    if-eqz v1, :cond_24

    .line 186
    :try_start_21
    invoke-virtual {v1}, Ljava/io/InputStream;->close()V
    :try_end_24
    .catch Ljava/io/IOException; {:try_start_21 .. :try_end_24} :catch_38

    .line 192
    :cond_24
    :goto_24
    return-object v0

    .line 181
    :catch_25
    move-exception v1

    move-object v1, v0

    .line 184
    :goto_27
    if-eqz v1, :cond_24

    .line 186
    :try_start_29
    invoke-virtual {v1}, Ljava/io/InputStream;->close()V
    :try_end_2c
    .catch Ljava/io/IOException; {:try_start_29 .. :try_end_2c} :catch_2d

    goto :goto_24

    .line 187
    :catch_2d
    move-exception v1

    goto :goto_24

    .line 184
    :catchall_2f
    move-exception v1

    move-object v2, v1

    move-object v3, v0

    :goto_32
    if-eqz v3, :cond_37

    .line 186
    :try_start_34
    invoke-virtual {v3}, Ljava/io/InputStream;->close()V
    :try_end_37
    .catch Ljava/io/IOException; {:try_start_34 .. :try_end_37} :catch_3a

    .line 191
    :cond_37
    :goto_37
    throw v2

    .line 187
    :catch_38
    move-exception v1

    goto :goto_24

    :catch_3a
    move-exception v0

    goto :goto_37

    .line 184
    :catchall_3c
    move-exception v0

    move-object v2, v0

    move-object v3, v1

    goto :goto_32

    .line 181
    :catch_40
    move-exception v2

    goto :goto_27
.end method

.method private static loadCertificates(Ljava/util/jar/JarFile;Ljava/util/jar/JarEntry;[B)[Ljava/security/cert/Certificate;
    .registers 7

    .prologue
    const/4 v0, 0x0

    .line 97
    :try_start_1
    new-instance v1, Ljava/io/BufferedInputStream;

    invoke-virtual {p0, p1}, Ljava/util/jar/JarFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    .line 99
    :cond_a
    const/4 v2, 0x0

    array-length v3, p2

    invoke-virtual {v1, p2, v2, v3}, Ljava/io/InputStream;->read([BII)I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_a

    .line 100
    invoke-virtual {v1}, Ljava/io/InputStream;->close()V

    .line 101
    if-eqz p1, :cond_1c

    invoke-virtual {p1}, Ljava/util/jar/JarEntry;->getCertificates()[Ljava/security/cert/Certificate;
    :try_end_1b
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1b} :catch_1f
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1b} :catch_1d

    move-result-object v0

    .line 105
    :cond_1c
    :goto_1c
    return-object v0

    .line 103
    :catch_1d
    move-exception v1

    goto :goto_1c

    .line 102
    :catch_1f
    move-exception v1

    goto :goto_1c
.end method
